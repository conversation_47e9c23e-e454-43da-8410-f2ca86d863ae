/**
 * 性能监控模块
 * 监控应用性能指标，包括API响应时间、内存使用、渲染性能等
 */

import { Logger } from './logger.js';
import { StorageManager } from './storage.js';

/**
 * 性能指标类型
 */
export const METRIC_TYPES = {
    API_RESPONSE: 'api_response',
    RENDER_TIME: 'render_time',
    MEMORY_USAGE: 'memory_usage',
    USER_INTERACTION: 'user_interaction',
    ERROR_RATE: 'error_rate'
};

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
    constructor() {
        this.logger = new Logger('PerformanceMonitor');
        this.storage = new StorageManager();
        this.metrics = new Map();
        this.timers = new Map();
        this.observers = new Map();
        this.isEnabled = true;
        this.maxMetrics = 1000;
        
        this.setupObservers();
    }

    /**
     * 设置性能观察器
     */
    setupObservers() {
        try {
            // 观察长任务
            if ('PerformanceObserver' in window) {
                const longTaskObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach(entry => {
                        this.recordMetric(METRIC_TYPES.RENDER_TIME, {
                            name: 'long_task',
                            duration: entry.duration,
                            startTime: entry.startTime
                        });
                    });
                });

                try {
                    longTaskObserver.observe({ entryTypes: ['longtask'] });
                    this.observers.set('longtask', longTaskObserver);
                } catch (error) {
                    this.logger.warn('长任务观察器不支持:', error.message);
                }

                // 观察导航性能
                const navigationObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach(entry => {
                        this.recordMetric(METRIC_TYPES.RENDER_TIME, {
                            name: 'navigation',
                            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                            loadComplete: entry.loadEventEnd - entry.loadEventStart,
                            domInteractive: entry.domInteractive - entry.navigationStart
                        });
                    });
                });

                try {
                    navigationObserver.observe({ entryTypes: ['navigation'] });
                    this.observers.set('navigation', navigationObserver);
                } catch (error) {
                    this.logger.warn('导航观察器不支持:', error.message);
                }
            }

            // 监控内存使用（如果支持）
            if ('memory' in performance) {
                this.startMemoryMonitoring();
            }

            this.logger.info('性能观察器设置完成');
        } catch (error) {
            this.logger.error('设置性能观察器失败:', error);
        }
    }

    /**
     * 开始内存监控
     */
    startMemoryMonitoring() {
        const monitorMemory = () => {
            if (performance.memory) {
                this.recordMetric(METRIC_TYPES.MEMORY_USAGE, {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                });
            }
        };

        // 每30秒监控一次内存使用
        setInterval(monitorMemory, 30000);
        monitorMemory(); // 立即执行一次
    }

    /**
     * 开始计时
     */
    startTimer(name, metadata = {}) {
        if (!this.isEnabled) return;

        this.timers.set(name, {
            startTime: performance.now(),
            metadata: metadata
        });
    }

    /**
     * 结束计时并记录
     */
    endTimer(name, additionalData = {}) {
        if (!this.isEnabled) return null;

        const timer = this.timers.get(name);
        if (!timer) {
            this.logger.warn(`计时器不存在: ${name}`);
            return null;
        }

        const duration = performance.now() - timer.startTime;
        this.timers.delete(name);

        const metric = {
            name: name,
            duration: duration,
            ...timer.metadata,
            ...additionalData,
            timestamp: Date.now()
        };

        this.recordMetric(METRIC_TYPES.API_RESPONSE, metric);
        return duration;
    }

    /**
     * 记录性能指标
     */
    recordMetric(type, data) {
        if (!this.isEnabled) return;

        const metric = {
            type: type,
            timestamp: Date.now(),
            ...data
        };

        // 添加到指标集合
        if (!this.metrics.has(type)) {
            this.metrics.set(type, []);
        }

        const typeMetrics = this.metrics.get(type);
        typeMetrics.push(metric);

        // 限制指标数量
        if (typeMetrics.length > this.maxMetrics) {
            typeMetrics.splice(0, typeMetrics.length - this.maxMetrics);
        }

        // 记录到日志
        this.logger.debug(`性能指标 [${type}]:`, data);

        // 检查性能阈值
        this.checkPerformanceThresholds(metric);
    }

    /**
     * 检查性能阈值
     */
    checkPerformanceThresholds(metric) {
        const thresholds = {
            [METRIC_TYPES.API_RESPONSE]: 5000, // 5秒
            [METRIC_TYPES.RENDER_TIME]: 100,   // 100ms
            [METRIC_TYPES.MEMORY_USAGE]: 50 * 1024 * 1024 // 50MB
        };

        const threshold = thresholds[metric.type];
        if (!threshold) return;

        let value;
        switch (metric.type) {
            case METRIC_TYPES.API_RESPONSE:
            case METRIC_TYPES.RENDER_TIME:
                value = metric.duration;
                break;
            case METRIC_TYPES.MEMORY_USAGE:
                value = metric.used;
                break;
            default:
                return;
        }

        if (value > threshold) {
            this.logger.warn(`性能阈值超标 [${metric.type}]: ${value} > ${threshold}`, metric);
            this.recordMetric(METRIC_TYPES.ERROR_RATE, {
                name: 'performance_threshold_exceeded',
                type: metric.type,
                value: value,
                threshold: threshold
            });
        }
    }

    /**
     * 记录用户交互
     */
    recordUserInteraction(action, element, duration = null) {
        this.recordMetric(METRIC_TYPES.USER_INTERACTION, {
            action: action,
            element: element,
            duration: duration,
            timestamp: Date.now()
        });
    }

    /**
     * 获取性能统计
     */
    getPerformanceStats(type = null, timeRange = 3600000) { // 默认1小时
        const now = Date.now();
        const cutoff = now - timeRange;
        const stats = {};

        const typesToAnalyze = type ? [type] : Array.from(this.metrics.keys());

        typesToAnalyze.forEach(metricType => {
            const metrics = this.metrics.get(metricType) || [];
            const recentMetrics = metrics.filter(m => m.timestamp > cutoff);

            if (recentMetrics.length === 0) {
                stats[metricType] = { count: 0 };
                return;
            }

            const values = recentMetrics.map(m => {
                switch (metricType) {
                    case METRIC_TYPES.API_RESPONSE:
                    case METRIC_TYPES.RENDER_TIME:
                        return m.duration || 0;
                    case METRIC_TYPES.MEMORY_USAGE:
                        return m.used || 0;
                    default:
                        return 1;
                }
            }).filter(v => v > 0);

            if (values.length > 0) {
                stats[metricType] = {
                    count: recentMetrics.length,
                    min: Math.min(...values),
                    max: Math.max(...values),
                    avg: values.reduce((a, b) => a + b, 0) / values.length,
                    median: this.calculateMedian(values),
                    p95: this.calculatePercentile(values, 95),
                    recent: recentMetrics.slice(-10)
                };
            } else {
                stats[metricType] = { count: recentMetrics.length };
            }
        });

        return stats;
    }

    /**
     * 计算中位数
     */
    calculateMedian(values) {
        const sorted = [...values].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 === 0 
            ? (sorted[mid - 1] + sorted[mid]) / 2 
            : sorted[mid];
    }

    /**
     * 计算百分位数
     */
    calculatePercentile(values, percentile) {
        const sorted = [...values].sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[Math.max(0, index)];
    }

    /**
     * 获取性能报告
     */
    generatePerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.getPerformanceStats(),
            systemInfo: this.getSystemInfo(),
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    /**
     * 获取系统信息
     */
    getSystemInfo() {
        const info = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };

        // 添加内存信息（如果可用）
        if (performance.memory) {
            info.memory = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
        }

        // 添加连接信息（如果可用）
        if (navigator.connection) {
            info.connection = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            };
        }

        return info;
    }

    /**
     * 生成性能建议
     */
    generateRecommendations() {
        const stats = this.getPerformanceStats();
        const recommendations = [];

        // API响应时间建议
        if (stats[METRIC_TYPES.API_RESPONSE]) {
            const apiStats = stats[METRIC_TYPES.API_RESPONSE];
            if (apiStats.avg > 3000) {
                recommendations.push({
                    type: 'api_performance',
                    severity: 'medium',
                    message: 'API响应时间较慢，建议检查网络连接或API服务状态',
                    value: apiStats.avg
                });
            }
        }

        // 内存使用建议
        if (stats[METRIC_TYPES.MEMORY_USAGE]) {
            const memoryStats = stats[METRIC_TYPES.MEMORY_USAGE];
            if (memoryStats.avg > 30 * 1024 * 1024) { // 30MB
                recommendations.push({
                    type: 'memory_usage',
                    severity: 'low',
                    message: '内存使用量较高，建议定期清理缓存',
                    value: memoryStats.avg
                });
            }
        }

        // 渲染性能建议
        if (stats[METRIC_TYPES.RENDER_TIME]) {
            const renderStats = stats[METRIC_TYPES.RENDER_TIME];
            if (renderStats.avg > 50) {
                recommendations.push({
                    type: 'render_performance',
                    severity: 'medium',
                    message: '页面渲染时间较长，建议优化DOM操作',
                    value: renderStats.avg
                });
            }
        }

        return recommendations;
    }

    /**
     * 清理旧指标
     */
    cleanupOldMetrics(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
        const cutoff = Date.now() - maxAge;
        let cleaned = 0;

        this.metrics.forEach((metrics, type) => {
            const originalLength = metrics.length;
            const filtered = metrics.filter(m => m.timestamp > cutoff);
            this.metrics.set(type, filtered);
            cleaned += originalLength - filtered.length;
        });

        this.logger.info(`清理了 ${cleaned} 个过期性能指标`);
        return cleaned;
    }

    /**
     * 导出性能数据
     */
    exportMetrics(format = 'json') {
        const data = {
            timestamp: new Date().toISOString(),
            metrics: Object.fromEntries(this.metrics),
            stats: this.getPerformanceStats()
        };

        if (format === 'json') {
            return JSON.stringify(data, null, 2);
        } else if (format === 'csv') {
            return this.convertToCSV(data);
        }

        return data;
    }

    /**
     * 转换为CSV格式
     */
    convertToCSV(data) {
        const rows = [];
        rows.push(['Type', 'Timestamp', 'Name', 'Duration', 'Additional Data']);

        Object.entries(data.metrics).forEach(([type, metrics]) => {
            metrics.forEach(metric => {
                rows.push([
                    type,
                    new Date(metric.timestamp).toISOString(),
                    metric.name || '',
                    metric.duration || '',
                    JSON.stringify(metric)
                ]);
            });
        });

        return rows.map(row => row.join(',')).join('\n');
    }

    /**
     * 启用/禁用监控
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        this.logger.info(`性能监控已${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 销毁监控器
     */
    destroy() {
        // 断开所有观察器
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();

        // 清理数据
        this.metrics.clear();
        this.timers.clear();

        this.logger.info('性能监控器已销毁');
    }
}

// 创建全局性能监控器实例
export const globalPerformanceMonitor = new PerformanceMonitor();
