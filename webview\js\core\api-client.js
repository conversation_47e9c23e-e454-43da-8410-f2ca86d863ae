/**
 * API客户端模块
 * 负责与各种AI模型API的通信，支持硅基流动和千问模型
 */

import { Logger } from '../utils/logger.js';
import { StorageManager } from '../utils/storage.js';

/**
 * API提供商配置
 */
const API_PROVIDERS = {
    siliconflow: {
        name: '硅基流动',
        baseUrl: 'https://api.siliconflow.cn/v1',
        models: {
            'deepseek-ai/DeepSeek-R1': 'DeepSeek-R1',
            'Qwen/Qwen2.5-72B-Instruct': 'Qwen2.5-72B-Instruct',
            'meta-llama/Llama-3.1-70B-Instruct': 'Llama-3.1-70B-Instruct'
        },
        headers: (apiKey) => ({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        })
    },
    qwen: {
        name: '千问模型',
        baseUrl: 'https://dashscope.aliyuncs.com/api/v1',
        models: {
            'qwen-turbo': 'Qwen-Turbo',
            'qwen-plus': 'Qwen-Plus',
            'qwen-max': 'Qwen-Max'
        },
        headers: (apiKey) => ({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        })
    },
    custom: {
        name: '自定义API',
        baseUrl: '',
        models: {},
        headers: (apiKey) => ({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        })
    }
};

/**
 * API客户端类
 */
export class APIClient {
    constructor() {
        this.logger = new Logger('APIClient');
        this.storage = new StorageManager();
        this.config = null;
        this.requestTimeout = 30000; // 30秒超时
        this.maxRetries = 2;
    }

    /**
     * 初始化API客户端
     */
    async initialize() {
        try {
            this.config = await this.storage.get('apiConfig', {
                provider: 'siliconflow',
                apiKey: '',
                model: 'deepseek-ai/DeepSeek-R1',
                maxTokens: 2000,
                temperature: 0.7,
                timeout: 30000,
                retries: 2
            });

            this.requestTimeout = this.config.timeout || 30000;
            this.maxRetries = this.config.retries || 2;

            this.logger.info('API客户端初始化完成', {
                provider: this.config.provider,
                model: this.config.model
            });
        } catch (error) {
            this.logger.error('API客户端初始化失败:', error);
            throw error;
        }
    }

    /**
     * 更新配置
     */
    async updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        await this.storage.set('apiConfig', this.config);
        
        this.requestTimeout = this.config.timeout || 30000;
        this.maxRetries = this.config.retries || 2;
        
        this.logger.info('API配置已更新');
    }

    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            const response = await this.sendChatRequest([
                { role: 'user', content: '你好，请回复"连接测试成功"' }
            ], {
                maxTokens: 50,
                temperature: 0.1
            });

            if (response && response.content) {
                this.logger.info('API连接测试成功');
                return {
                    success: true,
                    message: '连接测试成功',
                    response: response.content
                };
            } else {
                throw new Error('API响应格式异常');
            }
        } catch (error) {
            this.logger.error('API连接测试失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 发送聊天请求
     */
    async sendChatRequest(messages, options = {}) {
        if (!this.config || !this.config.apiKey) {
            throw new Error('API密钥未配置');
        }

        const provider = API_PROVIDERS[this.config.provider];
        if (!provider) {
            throw new Error(`不支持的API提供商: ${this.config.provider}`);
        }

        const requestOptions = {
            maxTokens: options.maxTokens || this.config.maxTokens || 2000,
            temperature: options.temperature || this.config.temperature || 0.7,
            model: options.model || this.config.model
        };

        let lastError = null;
        
        // 重试机制
        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    this.logger.info(`API请求重试 ${attempt}/${this.maxRetries}`);
                    // 指数退避延迟
                    await this.delay(Math.pow(2, attempt) * 1000);
                }

                const response = await this.makeRequest(provider, messages, requestOptions);
                
                this.logger.info('API请求成功', {
                    provider: this.config.provider,
                    model: requestOptions.model,
                    attempt: attempt + 1
                });

                return response;
                
            } catch (error) {
                lastError = error;
                this.logger.warn(`API请求失败 (尝试 ${attempt + 1}/${this.maxRetries + 1}):`, error.message);
                
                // 如果是认证错误或配额错误，不进行重试
                if (error.status === 401 || error.status === 429) {
                    break;
                }
            }
        }

        throw lastError || new Error('API请求失败');
    }

    /**
     * 执行实际的API请求
     */
    async makeRequest(provider, messages, options) {
        const url = `${provider.baseUrl}/chat/completions`;
        const headers = provider.headers(this.config.apiKey);

        const requestBody = this.buildRequestBody(provider, messages, options);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const error = new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
                error.status = response.status;
                error.response = errorData;
                throw error;
            }

            const data = await response.json();
            return this.parseResponse(data);

        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error(`请求超时 (${this.requestTimeout / 1000}秒)`);
            }
            
            throw error;
        }
    }

    /**
     * 构建请求体
     */
    buildRequestBody(provider, messages, options) {
        const baseBody = {
            model: options.model,
            messages: messages,
            max_tokens: options.maxTokens,
            temperature: options.temperature,
            stream: false
        };

        // 根据不同提供商调整请求格式
        switch (this.config.provider) {
            case 'siliconflow':
                return {
                    ...baseBody,
                    response_format: { type: 'text' }
                };
            
            case 'qwen':
                return {
                    ...baseBody,
                    top_p: 0.8,
                    repetition_penalty: 1.1
                };
            
            default:
                return baseBody;
        }
    }

    /**
     * 解析API响应
     */
    parseResponse(data) {
        try {
            if (!data.choices || !data.choices[0]) {
                throw new Error('API响应格式异常: 缺少choices字段');
            }

            const choice = data.choices[0];
            const content = choice.message?.content || choice.text || '';

            if (!content) {
                throw new Error('API响应内容为空');
            }

            return {
                content: content.trim(),
                usage: data.usage || {},
                model: data.model || this.config.model,
                finishReason: choice.finish_reason || 'unknown'
            };

        } catch (error) {
            this.logger.error('解析API响应失败:', error);
            throw new Error('API响应解析失败: ' + error.message);
        }
    }

    /**
     * 获取支持的模型列表
     */
    getSupportedModels(provider = null) {
        const targetProvider = provider || this.config?.provider || 'siliconflow';
        return API_PROVIDERS[targetProvider]?.models || {};
    }

    /**
     * 获取API提供商列表
     */
    getProviders() {
        return Object.keys(API_PROVIDERS).map(key => ({
            key,
            name: API_PROVIDERS[key].name,
            models: API_PROVIDERS[key].models
        }));
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取当前配置
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * 检查配置是否有效
     */
    isConfigValid() {
        return !!(this.config && this.config.apiKey && this.config.provider && this.config.model);
    }

    /**
     * 估算token数量（简单估算）
     */
    estimateTokens(text) {
        // 简单的token估算：中文字符按2个token计算，英文单词按1个token计算
        const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
        const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(word => word.length > 0).length;
        
        return chineseChars * 2 + englishWords;
    }
}
