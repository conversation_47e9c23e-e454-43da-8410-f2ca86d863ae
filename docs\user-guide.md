# AI邮件助手 - 用户使用指南

## 概述

AI邮件助手是一个基于Coremail邮件客户端的智能插件，利用大模型AI技术为您提供邮件内容分析、摘要生成、待办事项提取等功能，帮助您更高效地处理邮件。

## 主要功能

### 🔍 邮件智能分析
- **自动摘要生成**：AI自动提取邮件核心内容，生成简洁摘要
- **重要性评估**：智能评估邮件重要程度，帮助优先级排序
- **待办事项提取**：自动识别邮件中的任务和行动项
- **关键信息提取**：提取时间、联系人、地点等关键信息

### ⚙️ 灵活配置
- **多模型支持**：支持硅基流动、千问等多种AI模型
- **自定义分析**：可配置分析功能的开启/关闭
- **语言设置**：支持中文、英文等多种语言
- **主题切换**：支持浅色/深色主题

## 安装与配置

### 系统要求
- Coremail邮件客户端（支持插件的版本）
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- 稳定的网络连接

### 安装步骤

1. **下载插件包**
   - 从官方渠道下载AI邮件助手插件包
   - 解压到本地目录

2. **安装到Coremail**
   - 打开Coremail邮件客户端
   - 进入插件管理界面
   - 选择"安装本地插件"
   - 选择解压后的插件目录中的`app/plugin`文件夹
   - 确认安装

3. **首次配置**
   - 安装完成后，在邮件界面右侧会出现"AI助手"按钮
   - 点击按钮打开插件界面
   - 点击"设置"按钮进行初始配置

### API配置

#### 硅基流动API配置
1. **获取API密钥**
   - 访问 [硅基流动官网](https://siliconflow.cn)
   - 注册账户并获取API密钥

2. **配置插件**
   - 在插件设置页面选择"硅基流动"作为API提供商
   - 输入您的API密钥
   - 选择合适的模型（推荐：DeepSeek-R1）
   - 点击"测试连接"验证配置
   - 保存配置

#### 其他API配置
- **千问模型**：即将支持，敬请期待
- **自定义API**：可配置自部署的模型服务

## 使用方法

### 基本使用流程

1. **选择邮件**
   - 在Coremail中打开或选择一封邮件
   - 插件会自动检测当前邮件

2. **开始分析**
   - 点击"开始分析"按钮
   - 等待AI分析完成（通常需要几秒钟）

3. **查看结果**
   - 在侧边栏查看分析结果
   - 包括摘要、重要性、待办事项等

### 功能详解

#### 邮件摘要
- **自动生成**：AI自动提取邮件核心内容
- **长度控制**：摘要长度适中，便于快速阅读
- **关键信息**：突出重要信息点

#### 重要性评估
- **评分系统**：1-10分评分，10分最重要
- **评估依据**：基于内容紧急程度、发送者重要性等
- **可视化显示**：进度条直观显示重要程度

#### 待办事项
- **自动识别**：识别邮件中的任务和行动项
- **优先级标记**：高/中/低优先级分类
- **截止时间**：提取时间相关信息
- **任务类型**：分类不同类型的任务

#### 关键信息
- **时间信息**：会议时间、截止日期等
- **联系人信息**：相关人员信息
- **地点信息**：会议地点、活动场所
- **数字信息**：金额、数量等重要数据

### 高级功能

#### 自动分析
- 在设置中开启"自动分析新邮件"
- 打开邮件时自动进行AI分析
- 提高工作效率

#### 结果导出
- 点击"导出结果"按钮
- 将分析结果保存为JSON文件
- 便于存档和分享

#### 批量处理
- 选择多封邮件进行批量分析
- 生成汇总报告
- 适用于邮件整理场景

## 界面说明

### 主界面布局
```
┌─────────────────────────────────┐
│ AI邮件助手                v1.0   │ ← 标题栏
├─────────────────────────────────┤
│ 连接状态: 已连接                 │ ← 状态指示器
│ 分析状态: 就绪                   │
├─────────────────────────────────┤
│ 当前邮件                         │ ← 邮件信息
│ 主题: 项目会议安排               │
│ 发送者: 张三                     │
├─────────────────────────────────┤
│ 📄 邮件摘要                     │ ← 分析结果
│ 关于下周项目会议的安排通知...     │
├─────────────────────────────────┤
│ ⭐ 重要性评估                   │
│ ████████░░ 8/10 (高)            │
├─────────────────────────────────┤
│ 📋 待办事项                     │
│ □ 准备会议材料                   │
│ □ 确认参会时间                   │
├─────────────────────────────────┤
│ [开始分析] [导出结果]            │ ← 操作按钮
└─────────────────────────────────┘
```

### 设置界面
- **API配置**：配置AI模型和密钥
- **分析配置**：选择启用的分析功能
- **界面配置**：主题、通知等设置
- **高级配置**：超时时间、重试次数等

## 常见问题

### Q: 插件无法正常工作怎么办？
A: 请检查以下几点：
1. 确认Coremail版本支持插件功能
2. 检查API密钥是否正确配置
3. 确认网络连接正常
4. 查看浏览器控制台是否有错误信息

### Q: AI分析速度很慢怎么办？
A: 可能的原因和解决方案：
1. 网络连接不稳定 - 检查网络状况
2. API服务繁忙 - 稍后重试
3. 邮件内容过长 - 系统会自动截断处理

### Q: 分析结果不准确怎么办？
A: 可以尝试：
1. 切换不同的AI模型
2. 调整分析语言设置
3. 检查邮件内容是否完整

### Q: 如何保护隐私和数据安全？
A: 插件采用以下安全措施：
1. API密钥本地加密存储
2. 邮件内容仅用于分析，不会存储
3. 支持本地部署的AI模型
4. 所有配置数据本地存储

### Q: 支持哪些语言？
A: 目前支持：
- 简体中文
- 繁体中文
- 英语
- 自动检测语言

## 技术支持

### 获取帮助
- **用户手册**：查看完整的用户手册
- **在线支持**：访问官方支持页面
- **社区论坛**：与其他用户交流经验

### 反馈问题
- **Bug报告**：通过官方渠道报告问题
- **功能建议**：提出新功能需求
- **使用体验**：分享使用心得

### 版本更新
- **自动检查**：插件会自动检查更新
- **手动更新**：可手动下载最新版本
- **更新日志**：查看版本更新内容

## 最佳实践

### 提高分析准确性
1. **完整邮件**：确保邮件内容完整
2. **清晰表达**：邮件内容表达清晰
3. **合适长度**：避免过长或过短的邮件

### 高效使用技巧
1. **快捷键**：熟悉常用快捷键
2. **批量处理**：利用批量分析功能
3. **自动化**：开启自动分析功能

### 配置优化
1. **模型选择**：根据需求选择合适模型
2. **功能配置**：只启用需要的分析功能
3. **性能调优**：调整超时和重试参数

---

*本指南会持续更新，请关注最新版本。如有疑问，请联系技术支持。*
