/**
 * 侧边栏UI组件
 * 负责管理主界面的显示和交互
 */

import { Logger } from '../utils/logger.js';

/**
 * 侧边栏UI类
 */
export class SidebarUI {
    constructor() {
        this.logger = new Logger('SidebarUI');
        this.elements = {};
        this.isInitialized = false;
        this.currentTheme = 'light';
        this.animationDuration = 300;
    }

    /**
     * 初始化UI组件
     */
    async initialize() {
        try {
            this.logger.info('初始化侧边栏UI');
            
            // 获取DOM元素
            this.bindElements();
            
            // 设置初始状态
            this.setupInitialState();
            
            // 绑定事件监听器
            this.bindEventListeners();
            
            // 应用主题
            this.applyTheme();
            
            this.isInitialized = true;
            this.logger.info('侧边栏UI初始化完成');
            
        } catch (error) {
            this.logger.error('侧边栏UI初始化失败:', error);
            throw error;
        }
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.elements = {
            // 状态指示器
            connectionStatus: document.getElementById('connectionStatus'),
            analysisStatus: document.getElementById('analysisStatus'),
            
            // 当前邮件信息
            currentMailInfo: document.getElementById('currentMailInfo'),
            
            // 分析结果卡片
            summaryContent: document.getElementById('summaryContent'),
            importanceContent: document.getElementById('importanceContent'),
            importanceBar: document.getElementById('importanceBar'),
            importanceText: document.getElementById('importanceText'),
            importanceReason: document.getElementById('importanceReason'),
            todoList: document.getElementById('todoList'),
            keyInfoContent: document.getElementById('keyInfoContent'),
            timeInfo: document.getElementById('timeInfo'),
            contactInfo: document.getElementById('contactInfo'),
            categoryInfo: document.getElementById('categoryInfo'),
            
            // 操作按钮
            refreshBtn: document.getElementById('refreshBtn'),
            configBtn: document.getElementById('configBtn'),
            analyzeBtn: document.getElementById('analyzeBtn'),
            exportBtn: document.getElementById('exportBtn'),
            
            // 加载和提示
            loadingOverlay: document.getElementById('loadingOverlay'),
            errorToast: document.getElementById('errorToast'),
            successToast: document.getElementById('successToast'),
            errorMessage: document.getElementById('errorMessage'),
            successMessage: document.getElementById('successMessage'),
            closeErrorBtn: document.getElementById('closeErrorBtn'),
            closeSuccessBtn: document.getElementById('closeSuccessBtn')
        };

        // 检查必要元素是否存在
        const requiredElements = ['connectionStatus', 'analysisStatus', 'currentMailInfo'];
        for (const elementId of requiredElements) {
            if (!this.elements[elementId]) {
                throw new Error(`必要的DOM元素不存在: ${elementId}`);
            }
        }
    }

    /**
     * 设置初始状态
     */
    setupInitialState() {
        // 设置初始状态文本
        this.updateConnectionStatus('未连接');
        this.updateAnalysisStatus('待机');
        
        // 禁用操作按钮
        this.setButtonEnabled('analyzeBtn', false);
        this.setButtonEnabled('exportBtn', false);
        
        // 隐藏加载和提示
        this.showLoading(false);
        this.hideToasts();
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 关闭提示按钮
        if (this.elements.closeErrorBtn) {
            this.elements.closeErrorBtn.addEventListener('click', () => {
                this.hideError();
            });
        }

        if (this.elements.closeSuccessBtn) {
            this.elements.closeSuccessBtn.addEventListener('click', () => {
                this.hideSuccess();
            });
        }

        // 自动隐藏提示
        this.setupAutoHideToasts();
    }

    /**
     * 更新连接状态
     */
    updateConnectionStatus(status) {
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.textContent = status;
            this.elements.connectionStatus.className = 'status-value';
            
            if (status === '已连接' || status === 'connected') {
                this.elements.connectionStatus.classList.add('connected');
            }
        }
    }

    /**
     * 更新分析状态
     */
    updateAnalysisStatus(status) {
        if (this.elements.analysisStatus) {
            this.elements.analysisStatus.textContent = status;
            this.elements.analysisStatus.className = 'status-value';
            
            if (status === '分析中' || status === 'analyzing') {
                this.elements.analysisStatus.classList.add('analyzing');
            }
        }
    }

    /**
     * 更新当前邮件信息
     */
    updateCurrentMail(mailInfo) {
        if (!this.elements.currentMailInfo) return;

        if (!mailInfo) {
            this.elements.currentMailInfo.innerHTML = '<p class="no-mail">请选择一封邮件进行分析</p>';
            this.setButtonEnabled('analyzeBtn', false);
            return;
        }

        const mailHtml = `
            <div class="mail-details">
                <div class="mail-detail-item">
                    <span class="detail-label">主题:</span>
                    <span class="detail-value">${this.escapeHtml(mailInfo.subject || '无主题')}</span>
                </div>
                <div class="mail-detail-item">
                    <span class="detail-label">发送者:</span>
                    <span class="detail-value">${this.escapeHtml(mailInfo.sender?.display || '未知')}</span>
                </div>
                <div class="mail-detail-item">
                    <span class="detail-label">时间:</span>
                    <span class="detail-value">${this.formatDate(mailInfo.mailTime)}</span>
                </div>
                <div class="mail-detail-item">
                    <span class="detail-label">大小:</span>
                    <span class="detail-value">${this.formatFileSize(mailInfo.size || 0)}</span>
                </div>
            </div>
        `;

        this.elements.currentMailInfo.innerHTML = mailHtml;
        this.setButtonEnabled('analyzeBtn', true);
    }

    /**
     * 更新分析结果
     */
    updateAnalysisResults(results) {
        if (!results) return;

        // 更新摘要
        if (results.summary && this.elements.summaryContent) {
            this.updateSummary(results.summary);
        }

        // 更新重要性评估
        if (results.importance && this.elements.importanceContent) {
            this.updateImportance(results.importance);
        }

        // 更新待办事项
        if (results.todos && this.elements.todoList) {
            this.updateTodos(results.todos);
        }

        // 更新关键信息
        if (results.keyInfo) {
            this.updateKeyInfo(results.keyInfo);
        }

        // 启用导出按钮
        this.setButtonEnabled('exportBtn', true);
    }

    /**
     * 更新摘要内容
     */
    updateSummary(summaryData) {
        if (summaryData.error) {
            this.elements.summaryContent.innerHTML = `<p class="error-text">摘要生成失败: ${summaryData.error}</p>`;
            return;
        }

        this.elements.summaryContent.innerHTML = `
            <p class="summary-text">${this.escapeHtml(summaryData.content || '无摘要内容')}</p>
            <div class="summary-meta">
                <span class="meta-item">字数: ${summaryData.wordCount || 0}</span>
                <span class="meta-item">模型: ${summaryData.model || 'unknown'}</span>
            </div>
        `;
    }

    /**
     * 更新重要性评估
     */
    updateImportance(importanceData) {
        if (importanceData.error) {
            this.elements.importanceContent.innerHTML = `<p class="error-text">重要性评估失败: ${importanceData.error}</p>`;
            return;
        }

        const score = importanceData.score || 5;
        const level = importanceData.level || 'medium';
        const reason = importanceData.reason || '无评估理由';

        // 更新重要性条
        if (this.elements.importanceBar) {
            const fillElement = this.elements.importanceBar.querySelector('.bar-fill');
            if (fillElement) {
                fillElement.style.width = `${(score / 10) * 100}%`;
            }
        }

        // 更新重要性文本
        if (this.elements.importanceText) {
            this.elements.importanceText.textContent = `${score}/10 (${level})`;
        }

        // 更新评估理由
        if (this.elements.importanceReason) {
            this.elements.importanceReason.innerHTML = `<p>${this.escapeHtml(reason)}</p>`;
        }
    }

    /**
     * 更新待办事项
     */
    updateTodos(todosData) {
        if (todosData.error) {
            this.elements.todoList.innerHTML = `<li class="error-text">待办事项提取失败: ${todosData.error}</li>`;
            return;
        }

        const todos = todosData.todos || [];
        
        if (todos.length === 0) {
            this.elements.todoList.innerHTML = '<li class="placeholder">暂无待办事项</li>';
            return;
        }

        const todoHtml = todos.map(todo => `
            <li class="todo-item">
                <input type="checkbox" class="todo-checkbox">
                <div class="todo-text">${this.escapeHtml(todo.task || '')}</div>
                <span class="todo-priority ${todo.priority || 'medium'}">${this.translatePriority(todo.priority)}</span>
            </li>
        `).join('');

        this.elements.todoList.innerHTML = todoHtml;
    }

    /**
     * 更新关键信息
     */
    updateKeyInfo(keyInfoData) {
        if (keyInfoData.error) {
            if (this.elements.timeInfo) this.elements.timeInfo.textContent = '提取失败';
            if (this.elements.contactInfo) this.elements.contactInfo.textContent = '提取失败';
            if (this.elements.categoryInfo) this.elements.categoryInfo.textContent = '提取失败';
            return;
        }

        if (this.elements.timeInfo) {
            this.elements.timeInfo.textContent = keyInfoData.timeInfo || '-';
        }

        if (this.elements.contactInfo) {
            const contacts = keyInfoData.contacts || [];
            this.elements.contactInfo.textContent = contacts.length > 0 ? contacts.join(', ') : '-';
        }

        if (this.elements.categoryInfo) {
            this.elements.categoryInfo.textContent = keyInfoData.category || '-';
        }
    }

    /**
     * 清除分析结果
     */
    clearAnalysisResults() {
        // 清除摘要
        if (this.elements.summaryContent) {
            this.elements.summaryContent.innerHTML = '<p class="placeholder">暂无分析结果</p>';
        }

        // 清除重要性评估
        if (this.elements.importanceBar) {
            const fillElement = this.elements.importanceBar.querySelector('.bar-fill');
            if (fillElement) {
                fillElement.style.width = '0%';
            }
        }
        if (this.elements.importanceText) {
            this.elements.importanceText.textContent = '未评估';
        }
        if (this.elements.importanceReason) {
            this.elements.importanceReason.innerHTML = '<p class="placeholder">暂无评估结果</p>';
        }

        // 清除待办事项
        if (this.elements.todoList) {
            this.elements.todoList.innerHTML = '<li class="placeholder">暂无待办事项</li>';
        }

        // 清除关键信息
        if (this.elements.timeInfo) this.elements.timeInfo.textContent = '-';
        if (this.elements.contactInfo) this.elements.contactInfo.textContent = '-';
        if (this.elements.categoryInfo) this.elements.categoryInfo.textContent = '-';

        // 禁用导出按钮
        this.setButtonEnabled('exportBtn', false);
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        if (this.elements.loadingOverlay) {
            this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
        }

        // 更新分析按钮状态
        if (this.elements.analyzeBtn) {
            const btnText = this.elements.analyzeBtn.querySelector('.btn-text');
            const btnLoading = this.elements.analyzeBtn.querySelector('.btn-loading');
            
            if (btnText && btnLoading) {
                btnText.style.display = show ? 'none' : 'inline';
                btnLoading.style.display = show ? 'inline' : 'none';
            }
            
            this.elements.analyzeBtn.disabled = show;
        }
    }

    /**
     * 显示错误提示
     */
    showError(message) {
        if (this.elements.errorToast && this.elements.errorMessage) {
            this.elements.errorMessage.textContent = message;
            this.elements.errorToast.style.display = 'flex';
            
            // 自动隐藏
            setTimeout(() => this.hideError(), 5000);
        }
    }

    /**
     * 显示成功提示
     */
    showSuccess(message) {
        if (this.elements.successToast && this.elements.successMessage) {
            this.elements.successMessage.textContent = message;
            this.elements.successToast.style.display = 'flex';
            
            // 自动隐藏
            setTimeout(() => this.hideSuccess(), 3000);
        }
    }

    /**
     * 显示警告提示
     */
    showWarning(message) {
        // 使用错误提示样式显示警告
        this.showError(message);
    }

    /**
     * 隐藏错误提示
     */
    hideError() {
        if (this.elements.errorToast) {
            this.elements.errorToast.style.display = 'none';
        }
    }

    /**
     * 隐藏成功提示
     */
    hideSuccess() {
        if (this.elements.successToast) {
            this.elements.successToast.style.display = 'none';
        }
    }

    /**
     * 隐藏所有提示
     */
    hideToasts() {
        this.hideError();
        this.hideSuccess();
    }

    /**
     * 设置按钮启用状态
     */
    setButtonEnabled(buttonId, enabled) {
        const button = this.elements[buttonId];
        if (button) {
            button.disabled = !enabled;
        }
    }

    /**
     * 应用主题
     */
    applyTheme(theme = 'light') {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
    }

    /**
     * 设置自动隐藏提示
     */
    setupAutoHideToasts() {
        // 点击提示外部区域隐藏
        document.addEventListener('click', (event) => {
            if (this.elements.errorToast && 
                this.elements.errorToast.style.display !== 'none' &&
                !this.elements.errorToast.contains(event.target)) {
                this.hideError();
            }
            
            if (this.elements.successToast && 
                this.elements.successToast.style.display !== 'none' &&
                !this.elements.successToast.contains(event.target)) {
                this.hideSuccess();
            }
        });
    }

    /**
     * 工具函数 - HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 工具函数 - 格式化日期
     */
    formatDate(date) {
        if (!date) return '-';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '-';
        
        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 工具函数 - 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 工具函数 - 翻译优先级
     */
    translatePriority(priority) {
        const translations = {
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return translations[priority] || priority;
    }

    /**
     * 添加聊天消息
     */
    addChatMessage(role, content) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;

        // 创建头像
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = role === 'user' ? '👤' : '🤖';

        // 创建消息内容
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        if (typeof content === 'string') {
            messageContent.innerHTML = this.formatMessageContent(content);
        } else if (content && typeof content === 'object') {
            messageContent.innerHTML = this.formatStructuredContent(content);
        }

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        chatMessages.appendChild(messageDiv);

        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    /**
     * 格式化消息内容
     */
    formatMessageContent(content) {
        // 支持简单的Markdown格式
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    /**
     * 格式化结构化内容
     */
    formatStructuredContent(content) {
        switch (content.type) {
            case 'error':
                return `<div class="message-error">❌ ${content.content}</div>`;

            case 'notification':
                return `<div class="message-notification">${this.formatMessageContent(content.content)}</div>`;

            case 'analysis':
                return `<div class="message-analysis">${this.formatMessageContent(content.content)}</div>`;

            case 'alert':
                return `<div class="message-alert">${this.formatMessageContent(content.content)}</div>`;

            case 'structured':
                let html = `<div class="message-structured">${this.formatMessageContent(content.content)}`;
                if (content.data) {
                    html += this.renderStructuredData(content.data);
                }
                html += '</div>';
                return html;

            default:
                return this.formatMessageContent(content.content || content);
        }
    }

    /**
     * 渲染结构化数据
     */
    renderStructuredData(data) {
        let html = '<div class="structured-data">';

        if (data.mails && data.mails.length > 0) {
            html += '<div class="mail-list">';
            data.mails.forEach(mail => {
                html += `<div class="mail-item">
                    <div class="mail-subject">${mail.subject}</div>
                    <div class="mail-sender">${mail.sender.display}</div>
                </div>`;
            });
            html += '</div>';
        }

        if (data.todos && data.todos.length > 0) {
            html += '<div class="todo-list">';
            data.todos.forEach(todo => {
                const priorityIcon = todo.priority === 'high' ? '🔴' :
                                   todo.priority === 'medium' ? '🟡' : '🟢';
                html += `<div class="todo-item">
                    ${priorityIcon} ${todo.task}
                </div>`;
            });
            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    /**
     * 显示聊天加载状态
     */
    showChatLoading(show) {
        const sendBtn = document.getElementById('sendBtn');
        const chatInput = document.getElementById('chatInput');

        if (sendBtn) {
            sendBtn.disabled = show;
            sendBtn.innerHTML = show ? '<span class="loading-spinner">⏳</span>' : '<span class="send-icon">📤</span>';
        }

        if (chatInput) {
            chatInput.disabled = show;
        }

        if (show) {
            this.addChatMessage('assistant', {
                type: 'loading',
                content: '正在思考中...'
            });
        } else {
            // 移除加载消息
            const loadingMessages = document.querySelectorAll('.message-loading');
            loadingMessages.forEach(msg => msg.remove());
        }
    }

    /**
     * 清空聊天消息
     */
    clearChatMessages() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.innerHTML = `
                <div class="message assistant-message">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <p>对话记录已清空。有什么可以帮助您的吗？</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 更新监控状态
     */
    updateMonitorStatus(status) {
        const monitorStatusEl = document.getElementById('monitorStatus');
        if (monitorStatusEl) {
            monitorStatusEl.textContent = status;
            monitorStatusEl.className = status === '监控中' ? 'status-value connected' : 'status-value';
        }
    }

    /**
     * 显示信息消息
     */
    showInfo(message) {
        this.showSuccess(message);
    }

    /**
     * 销毁UI组件
     */
    destroy() {
        this.hideToasts();
        this.showLoading(false);
        this.elements = {};
        this.isInitialized = false;
        this.logger.info('侧边栏UI已销毁');
    }
}
