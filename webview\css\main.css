/* AI邮件助手 - 主样式文件 */

/* 基础重置和变量 */
:root {
  /* 颜色变量 */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  
  /* 文字颜色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  
  /* 边框和阴影 */
  --border-color: #e2e8f0;
  --border-radius: 8px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  
  --border-color: #334155;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
}

/* 主容器 */
.ai-assistant-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 100%;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.header-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-title h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.version {
  font-size: 12px;
  color: var(--text-muted);
  background-color: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  background-color: var(--bg-secondary);
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-md);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.status-value {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: var(--bg-tertiary);
}

.status-value.connected {
  background-color: var(--success-color);
  color: white;
}

.status-value.analyzing {
  background-color: var(--info-color);
  color: white;
}

/* 区域样式 */
.section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 邮件信息卡片 */
.mail-info {
  background-color: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: var(--spacing-md);
}

.mail-info .no-mail {
  color: var(--text-muted);
  text-align: center;
  font-style: italic;
}

.mail-details {
  display: grid;
  gap: var(--spacing-sm);
}

.mail-detail-item {
  display: flex;
  gap: var(--spacing-sm);
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 60px;
}

.detail-value {
  color: var(--text-primary);
  flex: 1;
}

/* 分析卡片 */
.analysis-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.2s ease;
}

.analysis-card:hover {
  box-shadow: var(--shadow-md);
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-md) 0;
  margin-bottom: var(--spacing-sm);
}

.card-content {
  padding: 0 var(--spacing-md) var(--spacing-md);
}

.placeholder {
  color: var(--text-muted);
  font-style: italic;
  text-align: center;
  padding: var(--spacing-lg);
}

/* 重要性评估 */
.importance-level {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.level-label {
  font-size: 12px;
  color: var(--text-secondary);
  min-width: 60px;
}

.importance-bar {
  flex: 1;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
  transition: width 0.3s ease;
}

.level-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 50px;
  text-align: right;
}

.importance-reason {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 待办事项列表 */
.todo-list {
  list-style: none;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-checkbox {
  margin-top: 2px;
}

.todo-text {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.todo-priority {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.todo-priority.high {
  background-color: var(--error-color);
  color: white;
}

.todo-priority.medium {
  background-color: var(--warning-color);
  color: white;
}

.todo-priority.low {
  background-color: var(--success-color);
  color: white;
}

/* 关键信息网格 */
.key-info-grid {
  display: grid;
  gap: var(--spacing-sm);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: 4px;
}

.info-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 500;
}

/* 操作按钮区域 */
.actions-section {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--bg-card);
  border-top: 1px solid var(--border-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-secondary);
}

.btn-icon {
  padding: var(--spacing-sm);
  min-width: 36px;
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-icon:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 加载和提示样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background-color: var(--bg-card);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--bg-tertiary);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 提示消息 */
.error-toast,
.success-toast {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  max-width: 300px;
}

.error-toast {
  background-color: var(--error-color);
  color: white;
}

.success-toast {
  background-color: var(--success-color);
  color: white;
}

.close-btn {
  background: none;
  border: none;
  color: inherit;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .header {
    padding: var(--spacing-sm);
  }
  
  .main-content {
    padding: var(--spacing-sm);
  }
  
  .status-indicator {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .actions-section {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}

/* 对话窗口样式 */
.chat-window {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.chat-messages {
  max-height: 300px;
  overflow-y: auto;
  padding: var(--spacing-md);
  background: var(--bg-primary);
}

.message {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  animation: fadeInUp 0.3s ease-out;
}

.message:last-child {
  margin-bottom: 0;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.assistant-message .message-avatar {
  background: var(--primary-color);
  color: white;
}

.user-message .message-avatar {
  background: var(--success-color);
  color: white;
}

.message-content {
  flex: 1;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
}

.user-message .message-content {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.message-content p {
  margin: 0 0 var(--spacing-xs) 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content ul {
  margin: var(--spacing-xs) 0;
  padding-left: var(--spacing-md);
}

.message-content li {
  margin-bottom: var(--spacing-xs);
}

/* 快速操作按钮 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

.quick-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.quick-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

/* 输入区域样式 */
.chat-input-area {
  background: var(--bg-card);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md);
}

.input-container {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

#chatInput {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

#chatInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.send-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.send-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

.send-icon {
  font-size: 16px;
}

.input-hint {
  margin-top: var(--spacing-xs);
  font-size: 11px;
  color: var(--text-muted);
  text-align: center;
}

/* 监控信息样式 */
.monitor-info {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.monitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.monitor-item:last-child {
  border-bottom: none;
}

.monitor-label {
  font-weight: 500;
  color: var(--text-secondary);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
