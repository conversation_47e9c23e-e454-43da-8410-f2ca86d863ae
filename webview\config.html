<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手配置</title>
    <link rel="stylesheet" href="css/config.css">
</head>
<body>
    <!-- 加载Coremail插件API -->
    <script src="plugin:plugin.js"></script>
    
    <div id="configApp" class="config-container">
        <!-- 头部 -->
        <header class="config-header">
            <h1>AI邮件助手 - 配置设置</h1>
            <p class="subtitle">配置您的AI模型和分析偏好</p>
        </header>

        <!-- 配置表单 -->
        <main class="config-main">
            <form id="configForm" class="config-form">
                <!-- API配置 -->
                <section class="config-section">
                    <h2 class="section-title">🔑 API配置</h2>
                    
                    <div class="form-group">
                        <label for="apiProvider" class="form-label">API提供商</label>
                        <select id="apiProvider" class="form-select" required>
                            <option value="siliconflow">硅基流动 (SiliconFlow)</option>
                            <option value="qwen" disabled>千问模型 (即将支持)</option>
                            <option value="custom">自定义API</option>
                        </select>
                        <small class="form-help">选择您要使用的AI模型提供商</small>
                    </div>

                    <div class="form-group">
                        <label for="apiKey" class="form-label">API密钥</label>
                        <div class="input-group">
                            <input type="password" id="apiKey" class="form-input" placeholder="请输入您的API密钥" required>
                            <button type="button" id="toggleApiKey" class="btn btn-icon">👁️</button>
                        </div>
                        <small class="form-help">您的API密钥将安全存储在本地</small>
                    </div>

                    <div class="form-group">
                        <label for="apiModel" class="form-label">模型选择</label>
                        <select id="apiModel" class="form-select" required>
                            <option value="deepseek-ai/DeepSeek-R1">DeepSeek-R1 (推荐)</option>
                            <option value="Qwen/Qwen2.5-72B-Instruct">Qwen2.5-72B-Instruct</option>
                            <option value="meta-llama/Llama-3.1-70B-Instruct">Llama-3.1-70B</option>
                        </select>
                        <small class="form-help">不同模型在分析能力和速度上有所差异</small>
                    </div>

                    <div class="form-group">
                        <button type="button" id="testApiBtn" class="btn btn-secondary">
                            <span class="btn-text">测试连接</span>
                            <span class="btn-loading" style="display: none;">测试中...</span>
                        </button>
                        <div id="apiTestResult" class="test-result" style="display: none;"></div>
                    </div>
                </section>

                <!-- 分析配置 -->
                <section class="config-section">
                    <h2 class="section-title">🧠 分析配置</h2>
                    
                    <div class="form-group">
                        <label class="form-label">启用的分析功能</label>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" id="enableSummary" checked>
                                <span class="checkmark"></span>
                                邮件摘要生成
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="enableImportance" checked>
                                <span class="checkmark"></span>
                                重要性评估
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="enableTodos" checked>
                                <span class="checkmark"></span>
                                待办事项提取
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="enableKeyInfo" checked>
                                <span class="checkmark"></span>
                                关键信息提取
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="analysisLanguage" class="form-label">分析语言</label>
                        <select id="analysisLanguage" class="form-select">
                            <option value="zh-CN">简体中文</option>
                            <option value="zh-TW">繁体中文</option>
                            <option value="en">English</option>
                            <option value="auto">自动检测</option>
                        </select>
                        <small class="form-help">AI分析结果的输出语言</small>
                    </div>

                    <div class="form-group">
                        <label for="maxTokens" class="form-label">最大分析长度</label>
                        <input type="range" id="maxTokens" class="form-range" min="500" max="4000" value="2000" step="100">
                        <div class="range-labels">
                            <span>500</span>
                            <span id="maxTokensValue">2000</span>
                            <span>4000</span>
                        </div>
                        <small class="form-help">更长的分析长度可能产生更详细的结果，但会消耗更多API配额</small>
                    </div>
                </section>

                <!-- 界面配置 -->
                <section class="config-section">
                    <h2 class="section-title">🎨 界面配置</h2>
                    
                    <div class="form-group">
                        <label class="form-label">界面主题</label>
                        <div class="radio-group">
                            <label class="radio-item">
                                <input type="radio" name="theme" value="light" checked>
                                <span class="radio-mark"></span>
                                浅色主题
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="theme" value="dark">
                                <span class="radio-mark"></span>
                                深色主题
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="theme" value="auto">
                                <span class="radio-mark"></span>
                                跟随系统
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="autoAnalysis">
                            <span class="checkmark"></span>
                            自动分析新邮件
                        </label>
                        <small class="form-help">选中后，打开邮件时将自动进行AI分析</small>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="showNotifications" checked>
                            <span class="checkmark"></span>
                            显示分析完成通知
                        </label>
                    </div>
                </section>

                <!-- 高级配置 -->
                <section class="config-section">
                    <h2 class="section-title">⚙️ 高级配置</h2>
                    
                    <div class="form-group">
                        <label for="requestTimeout" class="form-label">请求超时时间 (秒)</label>
                        <input type="number" id="requestTimeout" class="form-input" min="10" max="120" value="30">
                        <small class="form-help">API请求的最大等待时间</small>
                    </div>

                    <div class="form-group">
                        <label for="retryAttempts" class="form-label">重试次数</label>
                        <input type="number" id="retryAttempts" class="form-input" min="0" max="5" value="2">
                        <small class="form-help">API请求失败时的重试次数</small>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="enableDebugLog">
                            <span class="checkmark"></span>
                            启用调试日志
                        </label>
                        <small class="form-help">开启后将记录详细的调试信息，有助于问题排查</small>
                    </div>
                </section>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">保存配置</span>
                        <span class="btn-loading" style="display: none;">保存中...</span>
                    </button>
                    <button type="button" id="resetBtn" class="btn btn-secondary">
                        重置为默认
                    </button>
                    <button type="button" id="exportConfigBtn" class="btn btn-outline">
                        导出配置
                    </button>
                    <button type="button" id="importConfigBtn" class="btn btn-outline">
                        导入配置
                    </button>
                </div>
            </form>
        </main>

        <!-- 隐藏的文件输入 -->
        <input type="file" id="configFileInput" accept=".json" style="display: none;">

        <!-- 状态提示 -->
        <div id="configToast" class="toast" style="display: none;">
            <span id="toastIcon" class="toast-icon"></span>
            <span id="toastMessage" class="toast-message"></span>
            <button id="closeToastBtn" class="close-btn">×</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script type="module" src="js/config.js"></script>
</body>
</html>
