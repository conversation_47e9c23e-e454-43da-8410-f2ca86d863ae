# AI邮件助手 - 部署指南

## 项目概述

AI邮件助手是一个企业级的Coremail插件，集成了大模型AI技术，为用户提供智能邮件分析、摘要生成、待办事项提取等功能。

## 项目结构

```
coremail-ai-assistant/
├── app/
│   └── plugin/                    # Coremail插件主目录
│       ├── setting.js             # 插件配置文件
│       ├── icons/                 # 图标资源
│       │   ├── assistant.ico      # 主图标
│       │   └── analysis.ico       # 分析图标
│       └── webview/               # 插件页面资源
│           ├── index.html         # 主界面
│           ├── config.html        # 配置页面
│           ├── css/               # 样式文件
│           │   ├── main.css       # 主样式
│           │   └── config.css     # 配置页面样式
│           ├── js/                # JavaScript模块
│           │   ├── main.js        # 主入口
│           │   ├── config.js      # 配置页面脚本
│           │   ├── core/          # 核心模块
│           │   │   ├── api-client.js      # API客户端
│           │   │   ├── mail-monitor.js    # 邮件监控
│           │   │   ├── ai-analyzer.js     # AI分析引擎
│           │   │   └── config-manager.js  # 配置管理
│           │   ├── ui/            # UI组件
│           │   │   └── sidebar.js # 侧边栏组件
│           │   └── utils/         # 工具函数
│           │       ├── logger.js          # 日志系统
│           │       ├── storage.js         # 存储管理
│           │       ├── error-handler.js   # 错误处理
│           │       ├── performance-monitor.js # 性能监控
│           │       └── helpers.js         # 辅助函数
│           └── templates/         # HTML模板
│               ├── summary-template.html
│               └── todo-template.html
├── docs/                          # 文档
│   ├── user-guide.md             # 用户指南
│   ├── developer-guide.md        # 开发者文档
│   └── api-reference.md          # API参考
├── tests/                         # 测试文件
│   ├── unit/                     # 单元测试
│   │   ├── test-runner.js        # 测试框架
│   │   ├── api-client.test.js    # API客户端测试
│   │   └── ai-analyzer.test.js   # AI分析器测试
│   └── test-runner.html          # 测试运行页面
├── README.md                      # 项目说明
└── DEPLOYMENT.md                  # 部署指南（本文件）
```

## 核心功能

### ✅ 已实现功能

1. **邮件智能分析**
   - 自动摘要生成
   - 重要性评估（1-10分评分系统）
   - 待办事项提取
   - 关键信息提取（时间、联系人、地点等）

2. **多模型支持**
   - 硅基流动API集成（DeepSeek-R1, Qwen2.5-72B等）
   - 千问模型接口预留
   - 自定义API支持

3. **用户界面**
   - 响应式侧边栏设计
   - 实时分析结果展示
   - 浅色/深色主题切换
   - 直观的重要性可视化

4. **配置管理**
   - API密钥安全存储
   - 分析功能开关配置
   - 多语言支持（中文、英文）
   - 高级参数调优

5. **系统功能**
   - 完整的错误处理机制
   - 性能监控和优化
   - 详细的日志记录
   - 结果缓存机制

### 🔮 预留扩展功能

1. **智能回复**：用户输入想法，AI润色生成建议回复
2. **智能分类**：根据用户规则自动分类邮件
3. **会议助手**：自动安排会议和发送邀请

## 部署步骤

### 1. 环境准备

#### 系统要求
- Coremail邮件客户端（支持插件功能的版本）
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- 稳定的网络连接

#### API服务准备
- 硅基流动账户和API密钥
- 或其他兼容的AI模型API服务

### 2. 插件安装

#### 方式一：直接安装（推荐）
1. 将整个 `app/plugin` 目录复制到Coremail插件目录
2. 重启Coremail客户端
3. 在邮件界面查看是否出现"AI助手"按钮

#### 方式二：打包安装
1. 将 `app/plugin` 目录打包为ZIP文件
2. 通过Coremail插件管理界面安装
3. 启用插件

### 3. 初始配置

#### 基础配置
1. 点击"AI助手"按钮打开插件
2. 点击"设置"按钮进入配置页面
3. 配置API信息：
   - 选择API提供商（硅基流动）
   - 输入API密钥
   - 选择AI模型
   - 测试连接

#### 高级配置
1. **分析配置**：
   - 启用/禁用特定分析功能
   - 设置分析语言
   - 调整Token限制

2. **界面配置**：
   - 选择主题（浅色/深色）
   - 开启/关闭自动分析
   - 配置通知设置

3. **性能配置**：
   - 调整请求超时时间
   - 设置重试次数
   - 启用调试日志

### 4. 功能验证

#### 基本功能测试
1. 选择一封邮件
2. 点击"开始分析"
3. 验证分析结果：
   - 摘要是否准确
   - 重要性评估是否合理
   - 待办事项是否正确提取

#### 高级功能测试
1. 测试自动分析功能
2. 验证结果导出功能
3. 检查配置导入/导出

### 5. 性能优化

#### 缓存配置
- 启用结果缓存以提高响应速度
- 设置合适的缓存过期时间
- 定期清理过期缓存

#### 网络优化
- 配置合适的超时时间
- 启用请求重试机制
- 监控API调用性能

## 故障排除

### 常见问题

#### 1. 插件无法加载
**症状**：邮件界面没有出现"AI助手"按钮

**解决方案**：
- 检查Coremail版本是否支持插件
- 验证插件目录结构是否正确
- 查看浏览器控制台错误信息
- 重启Coremail客户端

#### 2. API调用失败
**症状**：分析时提示"API请求失败"

**解决方案**：
- 验证API密钥是否正确
- 检查网络连接状态
- 确认API服务是否正常
- 查看错误日志详细信息

#### 3. 分析结果异常
**症状**：分析结果不准确或为空

**解决方案**：
- 检查邮件内容是否完整
- 尝试切换不同的AI模型
- 调整分析语言设置
- 查看API响应日志

#### 4. 性能问题
**症状**：分析速度很慢或界面卡顿

**解决方案**：
- 启用结果缓存
- 调整Token限制
- 优化网络设置
- 清理浏览器缓存

### 调试工具

#### 1. 开启调试日志
```javascript
// 在浏览器控制台执行
localStorage.setItem('ai-assistant:config', JSON.stringify({
    ...JSON.parse(localStorage.getItem('ai-assistant:config') || '{}'),
    enableDebugLog: true
}));
```

#### 2. 查看性能监控
```javascript
// 在浏览器控制台执行
window.aiAssistant?.performanceMonitor?.getPerformanceStats();
```

#### 3. 导出错误报告
```javascript
// 在浏览器控制台执行
window.aiAssistant?.errorHandler?.exportErrorReport();
```

## 维护和更新

### 定期维护
1. **清理缓存**：定期清理过期的分析缓存
2. **日志管理**：定期清理旧的日志文件
3. **性能监控**：监控API调用性能和响应时间
4. **错误分析**：分析错误报告并优化

### 版本更新
1. **备份配置**：更新前导出用户配置
2. **替换文件**：用新版本替换插件文件
3. **恢复配置**：导入之前的用户配置
4. **功能验证**：测试新版本功能是否正常

### 监控指标
- API调用成功率
- 平均响应时间
- 错误发生频率
- 用户使用频率

## 安全考虑

### 数据安全
- API密钥本地加密存储
- 邮件内容仅用于分析，不会持久化存储
- 支持本地部署的AI模型以提高数据安全性

### 网络安全
- 使用HTTPS进行API通信
- 实现请求签名验证（如API支持）
- 定期更新API密钥

### 隐私保护
- 用户可选择禁用特定分析功能
- 提供数据清理工具
- 支持完全离线的本地模型

## 技术支持

### 获取帮助
- 查看用户指南：`docs/user-guide.md`
- 阅读开发者文档：`docs/developer-guide.md`
- 参考API文档：`docs/api-reference.md`

### 问题反馈
- 通过官方渠道报告Bug
- 提交功能改进建议
- 参与社区讨论

### 开发支持
- 查看源代码注释
- 运行测试套件：`tests/test-runner.html`
- 参考示例代码

---

**部署完成后，您的AI邮件助手就可以开始为您提供智能的邮件分析服务了！**

如有任何问题，请参考相关文档或联系技术支持。
