/**
 * 全局错误处理器
 * 提供统一的错误处理、错误报告和恢复机制
 */

import { Logger } from './logger.js';
import { StorageManager } from './storage.js';

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
    NETWORK: 'network',
    API: 'api',
    VALIDATION: 'validation',
    STORAGE: 'storage',
    PLUGIN: 'plugin',
    UNKNOWN: 'unknown'
};

/**
 * 错误严重程度枚举
 */
export const ERROR_SEVERITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};

/**
 * 自定义错误类
 */
export class AIAssistantError extends Error {
    constructor(message, type = ERROR_TYPES.UNKNOWN, severity = ERROR_SEVERITY.MEDIUM, details = {}) {
        super(message);
        this.name = 'AIAssistantError';
        this.type = type;
        this.severity = severity;
        this.details = details;
        this.timestamp = new Date().toISOString();
        this.stack = (new Error()).stack;
    }

    toJSON() {
        return {
            name: this.name,
            message: this.message,
            type: this.type,
            severity: this.severity,
            details: this.details,
            timestamp: this.timestamp,
            stack: this.stack
        };
    }
}

/**
 * 全局错误处理器类
 */
export class ErrorHandler {
    constructor() {
        this.logger = new Logger('ErrorHandler');
        this.storage = new StorageManager();
        this.errorQueue = [];
        this.maxErrorQueue = 100;
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        this.listeners = new Map();
        
        this.setupGlobalHandlers();
    }

    /**
     * 设置全局错误处理器
     */
    setupGlobalHandlers() {
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(new AIAssistantError(
                event.reason?.message || 'Unhandled Promise Rejection',
                ERROR_TYPES.UNKNOWN,
                ERROR_SEVERITY.HIGH,
                { reason: event.reason }
            ));
            event.preventDefault();
        });

        // 捕获全局JavaScript错误
        window.addEventListener('error', (event) => {
            this.handleError(new AIAssistantError(
                event.message || 'Global JavaScript Error',
                ERROR_TYPES.UNKNOWN,
                ERROR_SEVERITY.HIGH,
                {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error
                }
            ));
        });

        this.logger.info('全局错误处理器已设置');
    }

    /**
     * 处理错误
     */
    async handleError(error, context = {}) {
        try {
            // 标准化错误对象
            const standardError = this.standardizeError(error, context);
            
            // 记录错误
            this.logError(standardError);
            
            // 添加到错误队列
            this.addToErrorQueue(standardError);
            
            // 尝试恢复
            await this.attemptRecovery(standardError);
            
            // 通知监听器
            this.notifyListeners('error', standardError);
            
            // 根据严重程度决定是否显示用户提示
            if (this.shouldShowUserNotification(standardError)) {
                this.showUserNotification(standardError);
            }
            
        } catch (handlingError) {
            // 错误处理过程中出现错误
            console.error('错误处理器自身出现错误:', handlingError);
            this.logger.error('错误处理器自身出现错误:', handlingError);
        }
    }

    /**
     * 标准化错误对象
     */
    standardizeError(error, context = {}) {
        if (error instanceof AIAssistantError) {
            return { ...error.toJSON(), context };
        }

        // 根据错误特征判断类型
        let type = ERROR_TYPES.UNKNOWN;
        let severity = ERROR_SEVERITY.MEDIUM;

        if (error.name === 'TypeError' || error.name === 'ReferenceError') {
            type = ERROR_TYPES.UNKNOWN;
            severity = ERROR_SEVERITY.HIGH;
        } else if (error.message?.includes('fetch') || error.message?.includes('network')) {
            type = ERROR_TYPES.NETWORK;
            severity = ERROR_SEVERITY.MEDIUM;
        } else if (error.message?.includes('API') || error.status) {
            type = ERROR_TYPES.API;
            severity = ERROR_SEVERITY.MEDIUM;
        } else if (error.message?.includes('storage') || error.message?.includes('localStorage')) {
            type = ERROR_TYPES.STORAGE;
            severity = ERROR_SEVERITY.LOW;
        }

        return {
            name: error.name || 'Error',
            message: error.message || 'Unknown error',
            type: type,
            severity: severity,
            details: {
                stack: error.stack,
                status: error.status,
                response: error.response,
                ...context
            },
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 记录错误日志
     */
    logError(error) {
        const logLevel = this.getLogLevel(error.severity);
        this.logger.log(logLevel, `[${error.type.toUpperCase()}] ${error.message}`, error.details);
    }

    /**
     * 获取日志级别
     */
    getLogLevel(severity) {
        switch (severity) {
            case ERROR_SEVERITY.LOW:
                return 1; // INFO
            case ERROR_SEVERITY.MEDIUM:
                return 2; // WARN
            case ERROR_SEVERITY.HIGH:
            case ERROR_SEVERITY.CRITICAL:
                return 3; // ERROR
            default:
                return 2; // WARN
        }
    }

    /**
     * 添加到错误队列
     */
    addToErrorQueue(error) {
        this.errorQueue.push(error);
        
        // 限制队列大小
        if (this.errorQueue.length > this.maxErrorQueue) {
            this.errorQueue = this.errorQueue.slice(-this.maxErrorQueue);
        }

        // 持久化错误队列
        this.persistErrorQueue();
    }

    /**
     * 持久化错误队列
     */
    async persistErrorQueue() {
        try {
            await this.storage.set('errorQueue', this.errorQueue, {
                ttl: 7 * 24 * 60 * 60 * 1000 // 7天过期
            });
        } catch (error) {
            console.warn('持久化错误队列失败:', error);
        }
    }

    /**
     * 加载错误队列
     */
    async loadErrorQueue() {
        try {
            const savedQueue = await this.storage.get('errorQueue', []);
            this.errorQueue = Array.isArray(savedQueue) ? savedQueue : [];
        } catch (error) {
            console.warn('加载错误队列失败:', error);
            this.errorQueue = [];
        }
    }

    /**
     * 尝试错误恢复
     */
    async attemptRecovery(error) {
        const recoveryKey = `${error.type}_${error.message}`;
        const attempts = this.retryAttempts.get(recoveryKey) || 0;

        if (attempts >= this.maxRetries) {
            this.logger.warn(`错误恢复已达到最大重试次数: ${recoveryKey}`);
            return false;
        }

        this.retryAttempts.set(recoveryKey, attempts + 1);

        try {
            switch (error.type) {
                case ERROR_TYPES.NETWORK:
                    return await this.recoverFromNetworkError(error);
                
                case ERROR_TYPES.API:
                    return await this.recoverFromAPIError(error);
                
                case ERROR_TYPES.STORAGE:
                    return await this.recoverFromStorageError(error);
                
                case ERROR_TYPES.PLUGIN:
                    return await this.recoverFromPluginError(error);
                
                default:
                    return false;
            }
        } catch (recoveryError) {
            this.logger.error('错误恢复失败:', recoveryError);
            return false;
        }
    }

    /**
     * 网络错误恢复
     */
    async recoverFromNetworkError(error) {
        this.logger.info('尝试从网络错误中恢复');
        
        // 等待一段时间后重试
        await this.delay(2000);
        
        // 检查网络连接
        if (navigator.onLine) {
            this.notifyListeners('recovery', { type: 'network', success: true });
            return true;
        }
        
        return false;
    }

    /**
     * API错误恢复
     */
    async recoverFromAPIError(error) {
        this.logger.info('尝试从API错误中恢复');
        
        // 如果是认证错误，提示用户重新配置
        if (error.details?.status === 401) {
            this.notifyListeners('authError', error);
            return false;
        }
        
        // 如果是限流错误，等待后重试
        if (error.details?.status === 429) {
            await this.delay(5000);
            return true;
        }
        
        // 其他API错误，短暂等待后重试
        await this.delay(1000);
        return true;
    }

    /**
     * 存储错误恢复
     */
    async recoverFromStorageError(error) {
        this.logger.info('尝试从存储错误中恢复');
        
        try {
            // 清理存储空间
            await this.cleanupStorage();
            return true;
        } catch (cleanupError) {
            this.logger.error('存储清理失败:', cleanupError);
            return false;
        }
    }

    /**
     * 插件错误恢复
     */
    async recoverFromPluginError(error) {
        this.logger.info('尝试从插件错误中恢复');
        
        // 重新初始化插件相关组件
        this.notifyListeners('pluginRestart', error);
        return true;
    }

    /**
     * 清理存储空间
     */
    async cleanupStorage() {
        try {
            // 清理过期的缓存数据
            const keys = await this.storage.keys();
            for (const key of keys) {
                if (key.includes('cache_') || key.includes('temp_')) {
                    await this.storage.remove(key);
                }
            }
            this.logger.info('存储空间清理完成');
        } catch (error) {
            this.logger.error('存储空间清理失败:', error);
            throw error;
        }
    }

    /**
     * 判断是否应该显示用户通知
     */
    shouldShowUserNotification(error) {
        return error.severity === ERROR_SEVERITY.HIGH || 
               error.severity === ERROR_SEVERITY.CRITICAL ||
               error.type === ERROR_TYPES.API;
    }

    /**
     * 显示用户通知
     */
    showUserNotification(error) {
        const message = this.getUserFriendlyMessage(error);
        this.notifyListeners('userNotification', { error, message });
    }

    /**
     * 获取用户友好的错误消息
     */
    getUserFriendlyMessage(error) {
        const messages = {
            [ERROR_TYPES.NETWORK]: '网络连接出现问题，请检查网络设置',
            [ERROR_TYPES.API]: 'AI服务暂时不可用，请稍后重试或检查API配置',
            [ERROR_TYPES.STORAGE]: '数据存储出现问题，部分功能可能受影响',
            [ERROR_TYPES.PLUGIN]: '插件运行出现问题，正在尝试恢复',
            [ERROR_TYPES.VALIDATION]: '输入数据格式不正确，请检查后重试',
            [ERROR_TYPES.UNKNOWN]: '出现未知错误，请刷新页面重试'
        };

        return messages[error.type] || messages[ERROR_TYPES.UNKNOWN];
    }

    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {
            total: this.errorQueue.length,
            byType: {},
            bySeverity: {},
            recent: this.errorQueue.slice(-10)
        };

        this.errorQueue.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
        });

        return stats;
    }

    /**
     * 清空错误队列
     */
    async clearErrorQueue() {
        this.errorQueue = [];
        this.retryAttempts.clear();
        await this.storage.remove('errorQueue');
        this.logger.info('错误队列已清空');
    }

    /**
     * 导出错误报告
     */
    exportErrorReport() {
        const report = {
            timestamp: new Date().toISOString(),
            stats: this.getErrorStats(),
            errors: this.errorQueue,
            systemInfo: {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            }
        };

        return JSON.stringify(report, null, 2);
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件监听器执行失败 (${event}):`, error);
                }
            });
        }
    }
}

// 创建全局错误处理器实例
export const globalErrorHandler = new ErrorHandler();
