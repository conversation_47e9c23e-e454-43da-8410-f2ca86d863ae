<!-- 邮件摘要模板 -->
<div class="summary-template">
    <div class="summary-header">
        <h4 class="summary-title">📄 邮件摘要</h4>
        <div class="summary-actions">
            <button class="btn-icon copy-summary" title="复制摘要">📋</button>
            <button class="btn-icon expand-summary" title="展开/收起">📖</button>
        </div>
    </div>
    
    <div class="summary-content">
        <div class="summary-text">
            {{summaryContent}}
        </div>
        
        <div class="summary-metadata">
            <div class="metadata-item">
                <span class="metadata-label">字数:</span>
                <span class="metadata-value">{{wordCount}}</span>
            </div>
            <div class="metadata-item">
                <span class="metadata-label">模型:</span>
                <span class="metadata-value">{{model}}</span>
            </div>
            <div class="metadata-item">
                <span class="metadata-label">生成时间:</span>
                <span class="metadata-value">{{timestamp}}</span>
            </div>
        </div>
        
        <div class="summary-confidence" style="display: {{showConfidence ? 'block' : 'none'}}">
            <div class="confidence-bar">
                <div class="confidence-fill" style="width: {{confidence}}%"></div>
            </div>
            <span class="confidence-text">置信度: {{confidence}}%</span>
        </div>
    </div>
    
    <div class="summary-actions-footer">
        <button class="btn btn-sm btn-outline regenerate-summary">
            🔄 重新生成
        </button>
        <button class="btn btn-sm btn-outline export-summary">
            📤 导出摘要
        </button>
    </div>
</div>

<style>
.summary-template {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.summary-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.summary-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.summary-content {
    padding: var(--spacing-md);
}

.summary-text {
    font-size: 13px;
    line-height: 1.6;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.summary-metadata {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: 4px;
}

.metadata-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.metadata-label {
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
}

.metadata-value {
    font-size: 11px;
    color: var(--text-primary);
}

.summary-confidence {
    margin-bottom: var(--spacing-md);
}

.confidence-bar {
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--spacing-xs);
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--error-color), var(--warning-color), var(--success-color));
    transition: width 0.3s ease;
}

.confidence-text {
    font-size: 11px;
    color: var(--text-secondary);
}

.summary-actions-footer {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
    min-height: 28px;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-outline:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-icon {
    background: none;
    border: none;
    padding: var(--spacing-xs);
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .summary-metadata {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .summary-actions-footer {
        flex-direction: column;
    }
    
    .btn-sm {
        width: 100%;
    }
}
</style>
