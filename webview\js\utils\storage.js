/**
 * 存储管理模块
 * 提供统一的数据存储接口，支持localStorage和sessionStorage
 */

import { Logger } from './logger.js';

/**
 * 存储类型枚举
 */
const STORAGE_TYPES = {
    LOCAL: 'localStorage',
    SESSION: 'sessionStorage'
};

/**
 * 存储管理器类
 */
export class StorageManager {
    constructor(prefix = 'ai-assistant') {
        this.prefix = prefix;
        this.logger = new Logger('StorageManager');
        this.encryptionKey = null;
        
        // 检查存储可用性
        this.checkStorageAvailability();
    }

    /**
     * 检查存储可用性
     */
    checkStorageAvailability() {
        this.localStorageAvailable = this.isStorageAvailable('localStorage');
        this.sessionStorageAvailable = this.isStorageAvailable('sessionStorage');
        
        if (!this.localStorageAvailable && !this.sessionStorageAvailable) {
            this.logger.warn('浏览器存储不可用，将使用内存存储');
            this.memoryStorage = new Map();
        }
    }

    /**
     * 检查特定存储类型是否可用
     */
    isStorageAvailable(type) {
        try {
            const storage = window[type];
            const testKey = '__storage_test__';
            storage.setItem(testKey, 'test');
            storage.removeItem(testKey);
            return true;
        } catch (error) {
            this.logger.warn(`${type} 不可用:`, error.message);
            return false;
        }
    }

    /**
     * 生成存储键名
     */
    getKey(key) {
        return `${this.prefix}:${key}`;
    }

    /**
     * 获取存储对象
     */
    getStorage(type = STORAGE_TYPES.LOCAL) {
        if (type === STORAGE_TYPES.LOCAL && this.localStorageAvailable) {
            return localStorage;
        } else if (type === STORAGE_TYPES.SESSION && this.sessionStorageAvailable) {
            return sessionStorage;
        } else {
            return this.memoryStorage;
        }
    }

    /**
     * 存储数据
     */
    async set(key, value, options = {}) {
        try {
            const {
                type = STORAGE_TYPES.LOCAL,
                encrypt = false,
                ttl = null
            } = options;

            const storageKey = this.getKey(key);
            const storage = this.getStorage(type);

            // 准备存储的数据
            const data = {
                value: value,
                timestamp: Date.now(),
                ttl: ttl
            };

            // 序列化数据
            let serializedData = JSON.stringify(data);

            // 加密数据（如果需要）
            if (encrypt && this.encryptionKey) {
                serializedData = await this.encrypt(serializedData);
            }

            // 存储数据
            if (storage instanceof Map) {
                storage.set(storageKey, serializedData);
            } else {
                storage.setItem(storageKey, serializedData);
            }

            this.logger.debug(`数据已存储: ${key}`, { type, encrypt, ttl });
            return true;

        } catch (error) {
            this.logger.error(`存储数据失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 获取数据
     */
    async get(key, defaultValue = null, options = {}) {
        try {
            const {
                type = STORAGE_TYPES.LOCAL,
                decrypt = false
            } = options;

            const storageKey = this.getKey(key);
            const storage = this.getStorage(type);

            // 获取原始数据
            let rawData;
            if (storage instanceof Map) {
                rawData = storage.get(storageKey);
            } else {
                rawData = storage.getItem(storageKey);
            }

            if (rawData === null || rawData === undefined) {
                return defaultValue;
            }

            // 解密数据（如果需要）
            if (decrypt && this.encryptionKey) {
                rawData = await this.decrypt(rawData);
            }

            // 反序列化数据
            const data = JSON.parse(rawData);

            // 检查TTL
            if (data.ttl && Date.now() > data.timestamp + data.ttl) {
                this.logger.debug(`数据已过期: ${key}`);
                await this.remove(key, { type });
                return defaultValue;
            }

            this.logger.debug(`数据已获取: ${key}`, { type, decrypt });
            return data.value;

        } catch (error) {
            this.logger.error(`获取数据失败: ${key}`, error);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     */
    async remove(key, options = {}) {
        try {
            const { type = STORAGE_TYPES.LOCAL } = options;
            const storageKey = this.getKey(key);
            const storage = this.getStorage(type);

            if (storage instanceof Map) {
                storage.delete(storageKey);
            } else {
                storage.removeItem(storageKey);
            }

            this.logger.debug(`数据已删除: ${key}`, { type });
            return true;

        } catch (error) {
            this.logger.error(`删除数据失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 检查数据是否存在
     */
    async has(key, options = {}) {
        try {
            const { type = STORAGE_TYPES.LOCAL } = options;
            const storageKey = this.getKey(key);
            const storage = this.getStorage(type);

            if (storage instanceof Map) {
                return storage.has(storageKey);
            } else {
                return storage.getItem(storageKey) !== null;
            }

        } catch (error) {
            this.logger.error(`检查数据存在性失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 获取所有键名
     */
    async keys(options = {}) {
        try {
            const { type = STORAGE_TYPES.LOCAL } = options;
            const storage = this.getStorage(type);
            const prefixLength = this.prefix.length + 1;

            if (storage instanceof Map) {
                return Array.from(storage.keys())
                    .filter(key => key.startsWith(this.prefix + ':'))
                    .map(key => key.substring(prefixLength));
            } else {
                const keys = [];
                for (let i = 0; i < storage.length; i++) {
                    const key = storage.key(i);
                    if (key && key.startsWith(this.prefix + ':')) {
                        keys.push(key.substring(prefixLength));
                    }
                }
                return keys;
            }

        } catch (error) {
            this.logger.error('获取键名列表失败:', error);
            return [];
        }
    }

    /**
     * 清空所有数据
     */
    async clear(options = {}) {
        try {
            const { type = STORAGE_TYPES.LOCAL } = options;
            const keys = await this.keys({ type });

            for (const key of keys) {
                await this.remove(key, { type });
            }

            this.logger.info(`已清空所有数据 (${type})`);
            return true;

        } catch (error) {
            this.logger.error('清空数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     */
    getStorageInfo(type = STORAGE_TYPES.LOCAL) {
        try {
            const storage = this.getStorage(type);
            
            if (storage instanceof Map) {
                return {
                    type: 'memory',
                    used: storage.size,
                    available: Infinity,
                    percentage: 0
                };
            }

            // 计算已使用的存储空间
            let used = 0;
            for (let key in storage) {
                if (storage.hasOwnProperty(key)) {
                    used += storage[key].length + key.length;
                }
            }

            // 估算可用空间（大多数浏览器localStorage限制为5-10MB）
            const estimated = type === STORAGE_TYPES.LOCAL ? 5 * 1024 * 1024 : 5 * 1024 * 1024;
            
            return {
                type: type,
                used: used,
                available: estimated,
                percentage: Math.round((used / estimated) * 100)
            };

        } catch (error) {
            this.logger.error('获取存储信息失败:', error);
            return null;
        }
    }

    /**
     * 设置加密密钥
     */
    setEncryptionKey(key) {
        this.encryptionKey = key;
        this.logger.info('加密密钥已设置');
    }

    /**
     * 简单的加密函数（仅作示例，生产环境应使用更安全的加密方法）
     */
    async encrypt(data) {
        if (!this.encryptionKey) {
            return data;
        }

        try {
            // 这里使用简单的Base64编码作为示例
            // 生产环境应该使用Web Crypto API或其他安全的加密库
            return btoa(unescape(encodeURIComponent(data)));
        } catch (error) {
            this.logger.error('数据加密失败:', error);
            return data;
        }
    }

    /**
     * 简单的解密函数
     */
    async decrypt(encryptedData) {
        if (!this.encryptionKey) {
            return encryptedData;
        }

        try {
            return decodeURIComponent(escape(atob(encryptedData)));
        } catch (error) {
            this.logger.error('数据解密失败:', error);
            return encryptedData;
        }
    }

    /**
     * 导出数据
     */
    async exportData(options = {}) {
        try {
            const { type = STORAGE_TYPES.LOCAL, format = 'json' } = options;
            const keys = await this.keys({ type });
            const data = {};

            for (const key of keys) {
                data[key] = await this.get(key, null, { type });
            }

            if (format === 'json') {
                return JSON.stringify(data, null, 2);
            } else {
                return data;
            }

        } catch (error) {
            this.logger.error('导出数据失败:', error);
            return null;
        }
    }

    /**
     * 导入数据
     */
    async importData(data, options = {}) {
        try {
            const { type = STORAGE_TYPES.LOCAL, overwrite = false } = options;
            let importData = data;

            if (typeof data === 'string') {
                importData = JSON.parse(data);
            }

            let imported = 0;
            let skipped = 0;

            for (const [key, value] of Object.entries(importData)) {
                const exists = await this.has(key, { type });
                
                if (exists && !overwrite) {
                    skipped++;
                    continue;
                }

                await this.set(key, value, { type });
                imported++;
            }

            this.logger.info(`数据导入完成: 导入${imported}项, 跳过${skipped}项`);
            return { imported, skipped };

        } catch (error) {
            this.logger.error('导入数据失败:', error);
            return { imported: 0, skipped: 0 };
        }
    }
}

/**
 * 存储类型常量导出
 */
export { STORAGE_TYPES };
