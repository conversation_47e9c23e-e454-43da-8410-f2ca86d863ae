# 🤖 AI邮件助手 - Coremail智能插件

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-1.0.0-green.svg)](CHANGELOG.md)
[![Platform](https://img.shields.io/badge/platform-Coremail-orange.svg)](https://www.coremail.cn/)

> 基于大模型AI技术的智能邮件分析插件，为Coremail用户提供邮件摘要、重要性评估、待办事项提取等功能。

## ✨ 功能特性

### 💬 对话式AI助手
- **🤖 自然对话**：支持自然语言提问，如"今天我有什么重要邮件？"
- **🚀 智能问答**：AI理解用户意图，提供精准回答
- **⚡ 快速操作**：预设常用问题，一键快速查询
- **📱 实时交互**：流畅的对话体验，即问即答

### 🔍 智能邮件分析
- **📄 邮件摘要**：AI自动提取邮件核心内容，生成简洁摘要
- **⭐ 重要性评估**：智能评估邮件重要程度（1-10分评分）
- **📋 待办事项**：自动识别并提取邮件中的任务和行动项
- **🔍 关键信息**：提取时间、联系人、地点、数字等关键信息

### 📧 实时邮件监控
- **🔄 自动监控**：实时监控新邮件到达，无需手动刷新
- **🤖 自动分析**：新邮件自动进行AI分析和分类
- **🚨 智能提醒**：重要邮件智能提醒，不错过关键信息
- **📊 统计跟踪**：实时统计邮件处理情况

### ⚙️ 灵活配置
- **🌐 多模型支持**：支持硅基流动、千问等多种AI模型
- **🎨 主题切换**：支持浅色/深色主题
- **🌍 多语言**：支持中文、英文等多种语言
- **🔧 自定义配置**：可配置分析功能的开启/关闭

### 🚀 高性能
- **⚡ 智能缓存**：结果缓存机制，提高响应速度
- **🔄 自动重试**：网络异常时自动重试
- **📊 性能监控**：实时监控系统性能
- **🛡️ 错误处理**：完善的错误处理和恢复机制

## 🎯 使用场景

- **💬 智能问答**：通过自然对话快速获取邮件信息，如"今天有什么重要邮件？"
- **📧 邮件处理**：自动监控和分析新邮件，提供智能摘要和分类
- **⏰ 任务管理**：自动提取待办事项，智能提醒重要任务
- **📈 优先级排序**：根据重要性评估，合理安排工作优先级
- **🔍 信息查询**：快速查找特定邮件和关键信息

## 🚀 快速开始

### 系统要求
- Coremail邮件客户端（支持插件功能）
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- 硅基流动API密钥或其他兼容的AI模型API

### 安装步骤

1. **下载插件**
   ```bash
   git clone https://github.com/your-org/coremail-ai-assistant.git
   cd coremail-ai-assistant
   ```

2. **安装到Coremail**
   - 将 `app/plugin` 目录复制到Coremail插件目录
   - 重启Coremail客户端

3. **配置API**
   - 点击邮件界面的"AI助手"按钮
   - 进入设置页面配置API密钥
   - 测试连接并保存配置

### 基本使用

1. **启动助手**：点击Coremail工具栏的"AI助手"按钮
2. **自然对话**：在聊天框中输入问题，如"今天我有什么重要邮件？"
3. **查看回复**：AI助手会智能分析并提供详细回答
4. **快速操作**：使用预设的快速操作按钮进行常用查询

## 📚 文档

- [📖 用户指南](docs/user-guide.md) - 详细的使用说明
- [👨‍💻 开发者文档](docs/developer-guide.md) - 开发和扩展指南
- [📋 API参考](docs/api-reference.md) - 完整的API文档
- [🚀 部署指南](DEPLOYMENT.md) - 安装和部署说明

## 🛠️ 技术架构

### 技术栈
- **前端**：HTML5 + CSS3 + JavaScript (ES6+)
- **模块化**：ES6 Modules
- **API集成**：Fetch API + 硅基流动API
- **数据存储**：LocalStorage + SessionStorage
- **插件框架**：Coremail Plugin API

### 核心模块
```
📦 Core Modules
├── 🔌 API Client        # AI模型API通信
├── 🧠 AI Analyzer       # 邮件智能分析
├── 📧 Mail Monitor      # 邮件监控
├── ⚙️ Config Manager    # 配置管理
├── 🎨 UI Components     # 用户界面
└── 🛠️ Utils            # 工具函数
```

## 🧪 测试

### 运行测试
打开 `tests/test-runner.html` 在浏览器中运行完整的测试套件。

### 测试覆盖
- ✅ API客户端测试
- ✅ AI分析器测试
- ✅ 配置管理测试
- ✅ 集成测试

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 📧 邮箱：<EMAIL>
- 🐛 问题反馈：[GitHub Issues](https://github.com/your-org/coremail-ai-assistant/issues)
- 💬 讨论：[GitHub Discussions](https://github.com/your-org/coremail-ai-assistant/discussions)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

Made with ❤️ by AI Email Assistant Team

</div>

## 功能特性
- 🔍 **邮件监控与分析**: 实时监测邮件内容，智能分析重要性
- 🤖 **AI内容摘要**: 自动生成邮件摘要，快速了解邮件要点
- 📋 **待办事项提取**: 智能识别并提取邮件中的待办任务
- 🎯 **重要性评估**: AI评估邮件重要程度，优先级排序
- ⚙️ **灵活配置**: 支持多种大模型API，可自定义分析规则

## 技术架构

### 技术栈
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **API集成**: 硅基流动API (首期) + 千问模型 (预留)
- **数据存储**: LocalStorage + SessionStorage
- **模块化**: ES6 Modules

### 目录结构
```
coremail-ai-assistant/
├── app/
│   └── plugin/
│       ├── setting.js                 # 插件配置入口
│       ├── icons/                     # 图标资源
│       │   ├── assistant.ico
│       │   └── analysis.ico
│       └── webview/                   # 插件页面资源
│           ├── index.html             # 主界面
│           ├── config.html            # 配置页面
│           ├── css/                   # 样式文件
│           │   ├── main.css
│           │   └── config.css
│           ├── js/                    # JavaScript模块
│           │   ├── main.js            # 主入口
│           │   ├── core/              # 核心模块
│           │   │   ├── api-client.js  # API调用客户端
│           │   │   ├── mail-monitor.js # 邮件监控
│           │   │   ├── ai-analyzer.js  # AI分析引擎
│           │   │   └── config-manager.js # 配置管理
│           │   ├── ui/                # UI组件
│           │   │   ├── sidebar.js     # 侧边栏组件
│           │   │   ├── summary-card.js # 摘要卡片
│           │   │   └── todo-list.js   # 待办列表
│           │   └── utils/             # 工具函数
│           │       ├── logger.js      # 日志系统
│           │       ├── storage.js     # 存储管理
│           │       └── helpers.js     # 辅助函数
│           └── templates/             # HTML模板
│               ├── summary-template.html
│               └── todo-template.html
├── docs/                              # 文档
│   ├── user-guide.md                  # 用户指南
│   ├── developer-guide.md             # 开发者文档
│   └── api-reference.md               # API参考
└── tests/                             # 测试文件
    ├── unit/                          # 单元测试
    └── integration/                   # 集成测试
```

## 核心模块设计

### 1. API客户端模块 (api-client.js)
- 硅基流动API封装
- 千问模型接口预留
- 请求重试机制
- 错误处理

### 2. 邮件监控模块 (mail-monitor.js)
- 利用Coremail插件API获取邮件数据
- 邮件内容变化监听
- 数据预处理

### 3. AI分析引擎 (ai-analyzer.js)
- 邮件内容分析
- 摘要生成
- 待办事项提取
- 重要性评估

### 4. 用户界面模块 (ui/)
- 响应式侧边栏设计
- 实时数据展示
- 交互反馈

### 5. 配置管理模块 (config-manager.js)
- API密钥管理
- 用户偏好设置
- 分析规则配置

## 扩展性设计

### 后续功能预留
- 智能回复模块接口
- 邮件分类规则引擎
- 会议助手功能模块
- 多语言支持框架

### API适配层
- 统一的模型调用接口
- 多厂商API适配
- 模型切换无缝支持

## 开发计划
1. ✅ 项目架构设计
2. 🔄 Coremail插件基础框架
3. ⏳ 硅基流动API集成
4. ⏳ 邮件监控功能
5. ⏳ AI分析核心功能
6. ⏳ 用户界面实现
7. ⏳ 配置管理系统
8. ⏳ 错误处理与日志
9. ⏳ 测试与优化
10. ⏳ 文档编写

## 安装部署
详见 [用户指南](docs/user-guide.md)

## 开发贡献
详见 [开发者文档](docs/developer-guide.md)
