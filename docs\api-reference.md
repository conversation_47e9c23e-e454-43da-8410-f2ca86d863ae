# AI邮件助手 - API参考文档

## 概述

本文档详细描述了AI邮件助手的内部API接口，供开发者进行二次开发和集成使用。

## 核心类API

### APIClient 类

负责与AI模型API的通信。

#### 构造函数
```javascript
const apiClient = new APIClient();
```

#### 方法

##### `initialize()`
初始化API客户端。

```javascript
await apiClient.initialize();
```

**返回值**: `Promise<void>`

##### `sendChatRequest(messages, options)`
发送聊天请求到AI模型。

```javascript
const response = await apiClient.sendChatRequest([
    { role: 'user', content: '分析这封邮件' }
], {
    maxTokens: 2000,
    temperature: 0.7
});
```

**参数**:
- `messages` (Array): 消息数组
  - `role` (string): 角色 ('user', 'assistant', 'system')
  - `content` (string): 消息内容
- `options` (Object): 可选参数
  - `maxTokens` (number): 最大token数，默认2000
  - `temperature` (number): 温度参数，默认0.7
  - `model` (string): 模型名称，可选

**返回值**: `Promise<Object>`
```javascript
{
    content: string,      // 响应内容
    usage: Object,        // 使用统计
    model: string,        // 使用的模型
    finishReason: string  // 完成原因
}
```

##### `testConnection()`
测试API连接。

```javascript
const result = await apiClient.testConnection();
```

**返回值**: `Promise<Object>`
```javascript
{
    success: boolean,  // 是否成功
    message: string,   // 结果消息
    response?: string  // 响应内容（成功时）
}
```

##### `updateConfig(newConfig)`
更新API配置。

```javascript
await apiClient.updateConfig({
    provider: 'siliconflow',
    apiKey: 'new_api_key',
    model: 'deepseek-ai/DeepSeek-R1'
});
```

**参数**:
- `newConfig` (Object): 新配置对象

**返回值**: `Promise<void>`

##### `getSupportedModels(provider)`
获取支持的模型列表。

```javascript
const models = apiClient.getSupportedModels('siliconflow');
```

**参数**:
- `provider` (string): API提供商，可选

**返回值**: `Object` - 模型键值对

##### `getProviders()`
获取所有API提供商。

```javascript
const providers = apiClient.getProviders();
```

**返回值**: `Array<Object>`
```javascript
[
    {
        key: string,     // 提供商键名
        name: string,    // 显示名称
        models: Object   // 支持的模型
    }
]
```

##### `isConfigValid()`
检查配置是否有效。

```javascript
const isValid = apiClient.isConfigValid();
```

**返回值**: `boolean`

##### `estimateTokens(text)`
估算文本的token数量。

```javascript
const tokens = apiClient.estimateTokens('这是一段测试文本');
```

**参数**:
- `text` (string): 要估算的文本

**返回值**: `number` - 估算的token数

---

### AIAnalyzer 类

AI分析引擎，负责邮件内容分析。

#### 构造函数
```javascript
const analyzer = new AIAnalyzer();
```

#### 方法

##### `initialize()`
初始化分析器。

```javascript
await analyzer.initialize();
```

**返回值**: `Promise<void>`

##### `analyzeEmail(mailInfo)`
分析邮件内容。

```javascript
const results = await analyzer.analyzeEmail(mailInfo);
```

**参数**:
- `mailInfo` (Object): 邮件信息对象
  - `id` (string): 邮件ID
  - `subject` (string): 邮件主题
  - `sender` (Object): 发送者信息
  - `content` (string): 邮件内容
  - 其他邮件属性...

**返回值**: `Promise<Object>`
```javascript
{
    summary?: Object,      // 摘要结果
    importance?: Object,   // 重要性评估
    todos?: Object,        // 待办事项
    keyInfo?: Object,      // 关键信息
    metadata: Object       // 元数据
}
```

##### `generateSummary(mailInfo, language)`
生成邮件摘要。

```javascript
const summary = await analyzer.generateSummary(mailInfo, 'zh-CN');
```

**参数**:
- `mailInfo` (Object): 邮件信息
- `language` (string): 语言代码，默认'zh-CN'

**返回值**: `Promise<Object>`
```javascript
{
    content: string,    // 摘要内容
    wordCount: number,  // 字数
    model: string       // 使用的模型
}
```

##### `evaluateImportance(mailInfo, language)`
评估邮件重要性。

```javascript
const importance = await analyzer.evaluateImportance(mailInfo, 'zh-CN');
```

**参数**:
- `mailInfo` (Object): 邮件信息
- `language` (string): 语言代码

**返回值**: `Promise<Object>`
```javascript
{
    score: number,      // 重要性评分 (1-10)
    level: string,      // 重要性级别 ('low'|'medium'|'high')
    reason: string,     // 评估理由
    model: string       // 使用的模型
}
```

##### `extractTodos(mailInfo, language)`
提取待办事项。

```javascript
const todos = await analyzer.extractTodos(mailInfo, 'zh-CN');
```

**参数**:
- `mailInfo` (Object): 邮件信息
- `language` (string): 语言代码

**返回值**: `Promise<Object>`
```javascript
{
    todos: Array<Object>,  // 待办事项列表
    count: number,         // 待办事项数量
    model: string          // 使用的模型
}
```

待办事项对象结构：
```javascript
{
    task: string,          // 任务描述
    priority: string,      // 优先级 ('high'|'medium'|'low')
    deadline: string|null, // 截止时间
    type: string           // 任务类型
}
```

##### `extractKeyInfo(mailInfo, language)`
提取关键信息。

```javascript
const keyInfo = await analyzer.extractKeyInfo(mailInfo, 'zh-CN');
```

**参数**:
- `mailInfo` (Object): 邮件信息
- `language` (string): 语言代码

**返回值**: `Promise<Object>`
```javascript
{
    timeInfo: string,      // 时间信息
    contacts: Array,       // 联系人列表
    locations: Array,      // 地点信息
    numbers: Array,        // 数字信息
    category: string,      // 邮件分类
    model: string          // 使用的模型
}
```

##### `clearCache()`
清空分析缓存。

```javascript
analyzer.clearCache();
```

**返回值**: `void`

##### `getAnalysisStats()`
获取分析统计信息。

```javascript
const stats = analyzer.getAnalysisStats();
```

**返回值**: `Object`
```javascript
{
    cacheSize: number,           // 缓存大小
    isInitialized: boolean,      // 是否已初始化
    apiClientConfig: Object,     // API客户端配置
    supportedLanguages: Array    // 支持的语言
}
```

---

### MailMonitor 类

邮件监控器，负责监控邮件变化。

#### 构造函数
```javascript
const monitor = new MailMonitor();
```

#### 方法

##### `initialize()`
初始化监控器。

```javascript
await monitor.initialize();
```

**返回值**: `Promise<void>`

##### `getCurrentMail()`
获取当前邮件。

```javascript
const mail = monitor.getCurrentMail();
```

**返回值**: `Object|null` - 当前邮件信息或null

##### `getMailContent(mailId)`
获取邮件内容。

```javascript
const content = await monitor.getMailContent(mailId);
```

**参数**:
- `mailId` (string): 邮件ID，可选

**返回值**: `Promise<string>` - 邮件内容

##### `getMailStats()`
获取邮件统计信息。

```javascript
const stats = monitor.getMailStats();
```

**返回值**: `Object|null`
```javascript
{
    wordCount: number,        // 字数
    estimatedReadTime: number, // 预估阅读时间（分钟）
    hasAttachments: boolean,  // 是否有附件
    isImportant: boolean,     // 是否重要
    recipientCount: number    // 收件人数量
}
```

##### `setPollFrequency(frequency)`
设置轮询频率。

```javascript
monitor.setPollFrequency(3000); // 3秒
```

**参数**:
- `frequency` (number): 轮询间隔（毫秒）

**返回值**: `void`

##### `startMonitoring()`
开始监控。

```javascript
await monitor.startMonitoring();
```

**返回值**: `Promise<void>`

##### `stopMonitoring()`
停止监控。

```javascript
monitor.stopMonitoring();
```

**返回值**: `void`

#### 事件

##### `mailChanged`
邮件变化事件。

```javascript
monitor.on('mailChanged', (mailInfo) => {
    console.log('邮件已变化:', mailInfo);
});
```

**回调参数**:
- `mailInfo` (Object): 新的邮件信息

---

### ConfigManager 类

配置管理器，负责管理用户配置。

#### 构造函数
```javascript
const config = new ConfigManager();
```

#### 方法

##### `initialize()`
初始化配置管理器。

```javascript
await config.initialize();
```

**返回值**: `Promise<void>`

##### `getConfig(key)`
获取配置。

```javascript
// 获取所有配置
const allConfig = config.getConfig();

// 获取特定配置
const apiKey = config.getConfig('apiKey');
```

**参数**:
- `key` (string): 配置键名，可选

**返回值**: `any` - 配置值或配置对象

##### `setConfig(key, value)` 或 `setConfig(configObject)`
设置配置。

```javascript
// 设置单个配置
await config.setConfig('apiKey', 'new_key');

// 批量设置配置
await config.setConfig({
    apiKey: 'new_key',
    apiModel: 'new_model'
});
```

**参数**:
- `key` (string|Object): 配置键名或配置对象
- `value` (any): 配置值

**返回值**: `Promise<boolean>` - 是否成功

##### `resetConfig(keys)`
重置配置。

```javascript
// 重置所有配置
await config.resetConfig();

// 重置特定配置
await config.resetConfig(['apiKey', 'apiModel']);

// 重置单个配置
await config.resetConfig('apiKey');
```

**参数**:
- `keys` (string|Array): 要重置的配置键名，可选

**返回值**: `Promise<void>`

##### `getAPIConfig()`
获取API配置。

```javascript
const apiConfig = config.getAPIConfig();
```

**返回值**: `Object`
```javascript
{
    provider: string,    // API提供商
    apiKey: string,      // API密钥
    model: string,       // 模型名称
    baseUrl: string,     // 基础URL
    maxTokens: number,   // 最大token数
    temperature: number, // 温度参数
    timeout: number,     // 超时时间
    retries: number      // 重试次数
}
```

##### `getAnalysisConfig()`
获取分析配置。

```javascript
const analysisConfig = config.getAnalysisConfig();
```

**返回值**: `Object`
```javascript
{
    enableSummary: boolean,     // 启用摘要
    enableImportance: boolean,  // 启用重要性评估
    enableTodos: boolean,       // 启用待办事项
    enableKeyInfo: boolean,     // 启用关键信息
    language: string,           // 分析语言
    autoAnalysis: boolean       // 自动分析
}
```

##### `getUIConfig()`
获取界面配置。

```javascript
const uiConfig = config.getUIConfig();
```

**返回值**: `Object`
```javascript
{
    theme: string,              // 主题
    showNotifications: boolean, // 显示通知
    sidebarWidth: number,       // 侧边栏宽度
    compactMode: boolean        // 紧凑模式
}
```

##### `exportConfig(includeSecrets)`
导出配置。

```javascript
const exportData = config.exportConfig(false); // 不包含敏感信息
```

**参数**:
- `includeSecrets` (boolean): 是否包含敏感信息，默认false

**返回值**: `Object` - 导出的配置数据

##### `importConfig(configData, options)`
导入配置。

```javascript
const result = await config.importConfig(configData, {
    overwrite: false,
    validateOnly: false
});
```

**参数**:
- `configData` (string|Object): 配置数据
- `options` (Object): 导入选项
  - `overwrite` (boolean): 是否覆盖现有配置
  - `validateOnly` (boolean): 仅验证不导入

**返回值**: `Promise<Object>`
```javascript
{
    success: boolean,  // 是否成功
    message: string    // 结果消息
}
```

##### `isConfigComplete()`
检查配置是否完整。

```javascript
const isComplete = config.isConfigComplete();
```

**返回值**: `boolean`

#### 事件

##### `configChanged`
配置变更事件。

```javascript
config.on('configChanged', (newConfig) => {
    console.log('配置已更新:', newConfig);
});
```

**回调参数**:
- `newConfig` (Object): 新的配置对象

---

## 工具类API

### Logger 类

日志记录器。

#### 构造函数
```javascript
const logger = new Logger('ModuleName');
```

#### 方法

##### `debug(message, data)`
记录调试日志。

```javascript
logger.debug('调试信息', { key: 'value' });
```

##### `info(message, data)`
记录信息日志。

```javascript
logger.info('操作成功', { result: 'success' });
```

##### `warn(message, data)`
记录警告日志。

```javascript
logger.warn('警告信息', { warning: 'details' });
```

##### `error(message, data)`
记录错误日志。

```javascript
logger.error('错误信息', error);
```

##### `getLogs(level, limit)`
获取日志。

```javascript
const logs = logger.getLogs(LOG_LEVELS.ERROR, 10);
```

##### `clearLogs()`
清空日志。

```javascript
logger.clearLogs();
```

---

### StorageManager 类

存储管理器。

#### 构造函数
```javascript
const storage = new StorageManager('prefix');
```

#### 方法

##### `set(key, value, options)`
存储数据。

```javascript
await storage.set('key', 'value', {
    type: STORAGE_TYPES.LOCAL,
    encrypt: false,
    ttl: 3600000 // 1小时
});
```

##### `get(key, defaultValue, options)`
获取数据。

```javascript
const value = await storage.get('key', 'default', {
    type: STORAGE_TYPES.LOCAL,
    decrypt: false
});
```

##### `remove(key, options)`
删除数据。

```javascript
await storage.remove('key', {
    type: STORAGE_TYPES.LOCAL
});
```

##### `has(key, options)`
检查数据是否存在。

```javascript
const exists = await storage.has('key');
```

##### `keys(options)`
获取所有键名。

```javascript
const keys = await storage.keys();
```

##### `clear(options)`
清空所有数据。

```javascript
await storage.clear();
```

---

## 常量和枚举

### 错误类型
```javascript
export const ERROR_TYPES = {
    NETWORK: 'network',
    API: 'api',
    VALIDATION: 'validation',
    STORAGE: 'storage',
    PLUGIN: 'plugin',
    UNKNOWN: 'unknown'
};
```

### 错误严重程度
```javascript
export const ERROR_SEVERITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};
```

### 存储类型
```javascript
export const STORAGE_TYPES = {
    LOCAL: 'localStorage',
    SESSION: 'sessionStorage'
};
```

### 日志级别
```javascript
export const LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3
};
```

---

## 事件系统

所有核心类都支持事件监听机制：

```javascript
// 监听事件
instance.on('eventName', callback);

// 移除监听
instance.off('eventName', callback);

// 一次性监听
instance.once('eventName', callback);
```

---

*本API文档会随着版本更新而持续完善。*
