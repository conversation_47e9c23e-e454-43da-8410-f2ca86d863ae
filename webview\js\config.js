/**
 * 配置页面主文件
 * 负责配置页面的初始化和交互
 */

import { Logger } from './utils/logger.js';
import { StorageManager } from './utils/storage.js';
import { ConfigManager } from './core/config-manager.js';
import { APIClient } from './core/api-client.js';

class ConfigApp {
    constructor() {
        this.logger = new Logger('ConfigApp');
        this.storage = new StorageManager();
        this.config = new ConfigManager();
        this.apiClient = new APIClient();
        this.elements = {};
        this.isInitialized = false;
    }

    /**
     * 初始化配置应用
     */
    async initialize() {
        try {
            this.logger.info('初始化配置应用');
            
            // 初始化核心模块
            await this.config.initialize();
            await this.apiClient.initialize();
            
            // 绑定DOM元素
            this.bindElements();
            
            // 加载当前配置
            await this.loadCurrentConfig();
            
            // 绑定事件监听器
            this.bindEventListeners();
            
            this.isInitialized = true;
            this.logger.info('配置应用初始化完成');
            
        } catch (error) {
            this.logger.error('配置应用初始化失败:', error);
            this.showToast('error', '初始化失败: ' + error.message);
        }
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.elements = {
            // 表单元素
            configForm: document.getElementById('configForm'),
            apiProvider: document.getElementById('apiProvider'),
            apiKey: document.getElementById('apiKey'),
            apiModel: document.getElementById('apiModel'),
            toggleApiKey: document.getElementById('toggleApiKey'),
            testApiBtn: document.getElementById('testApiBtn'),
            apiTestResult: document.getElementById('apiTestResult'),
            
            // 分析配置
            enableSummary: document.getElementById('enableSummary'),
            enableImportance: document.getElementById('enableImportance'),
            enableTodos: document.getElementById('enableTodos'),
            enableKeyInfo: document.getElementById('enableKeyInfo'),
            analysisLanguage: document.getElementById('analysisLanguage'),
            maxTokens: document.getElementById('maxTokens'),
            maxTokensValue: document.getElementById('maxTokensValue'),
            
            // 界面配置
            themeRadios: document.querySelectorAll('input[name="theme"]'),
            autoAnalysis: document.getElementById('autoAnalysis'),
            showNotifications: document.getElementById('showNotifications'),
            
            // 高级配置
            requestTimeout: document.getElementById('requestTimeout'),
            retryAttempts: document.getElementById('retryAttempts'),
            enableDebugLog: document.getElementById('enableDebugLog'),
            
            // 操作按钮
            resetBtn: document.getElementById('resetBtn'),
            exportConfigBtn: document.getElementById('exportConfigBtn'),
            importConfigBtn: document.getElementById('importConfigBtn'),
            configFileInput: document.getElementById('configFileInput'),
            
            // 提示元素
            configToast: document.getElementById('configToast'),
            toastIcon: document.getElementById('toastIcon'),
            toastMessage: document.getElementById('toastMessage'),
            closeToastBtn: document.getElementById('closeToastBtn')
        };
    }

    /**
     * 加载当前配置
     */
    async loadCurrentConfig() {
        try {
            const currentConfig = this.config.getConfig();
            
            // API配置
            if (this.elements.apiProvider) {
                this.elements.apiProvider.value = currentConfig.apiProvider || 'siliconflow';
                this.updateModelOptions();
            }
            
            if (this.elements.apiKey) {
                this.elements.apiKey.value = currentConfig.apiKey || '';
            }
            
            if (this.elements.apiModel) {
                this.elements.apiModel.value = currentConfig.apiModel || 'deepseek-ai/DeepSeek-R1';
            }
            
            // 分析配置
            if (this.elements.enableSummary) {
                this.elements.enableSummary.checked = currentConfig.enableSummary !== false;
            }
            
            if (this.elements.enableImportance) {
                this.elements.enableImportance.checked = currentConfig.enableImportance !== false;
            }
            
            if (this.elements.enableTodos) {
                this.elements.enableTodos.checked = currentConfig.enableTodos !== false;
            }
            
            if (this.elements.enableKeyInfo) {
                this.elements.enableKeyInfo.checked = currentConfig.enableKeyInfo !== false;
            }
            
            if (this.elements.analysisLanguage) {
                this.elements.analysisLanguage.value = currentConfig.analysisLanguage || 'zh-CN';
            }
            
            if (this.elements.maxTokens) {
                this.elements.maxTokens.value = currentConfig.maxTokens || 2000;
                this.updateMaxTokensDisplay();
            }
            
            // 界面配置
            if (this.elements.themeRadios) {
                const theme = currentConfig.theme || 'light';
                this.elements.themeRadios.forEach(radio => {
                    radio.checked = radio.value === theme;
                });
            }
            
            if (this.elements.autoAnalysis) {
                this.elements.autoAnalysis.checked = currentConfig.autoAnalysis || false;
            }
            
            if (this.elements.showNotifications) {
                this.elements.showNotifications.checked = currentConfig.showNotifications !== false;
            }
            
            // 高级配置
            if (this.elements.requestTimeout) {
                this.elements.requestTimeout.value = (currentConfig.requestTimeout || 30000) / 1000;
            }
            
            if (this.elements.retryAttempts) {
                this.elements.retryAttempts.value = currentConfig.retryAttempts || 2;
            }
            
            if (this.elements.enableDebugLog) {
                this.elements.enableDebugLog.checked = currentConfig.enableDebugLog || false;
            }
            
            this.logger.info('配置加载完成');
            
        } catch (error) {
            this.logger.error('加载配置失败:', error);
            this.showToast('error', '加载配置失败: ' + error.message);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 表单提交
        if (this.elements.configForm) {
            this.elements.configForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveConfig();
            });
        }

        // API提供商变更
        if (this.elements.apiProvider) {
            this.elements.apiProvider.addEventListener('change', () => {
                this.updateModelOptions();
            });
        }

        // API密钥显示/隐藏
        if (this.elements.toggleApiKey) {
            this.elements.toggleApiKey.addEventListener('click', () => {
                this.toggleApiKeyVisibility();
            });
        }

        // API连接测试
        if (this.elements.testApiBtn) {
            this.elements.testApiBtn.addEventListener('click', () => {
                this.testApiConnection();
            });
        }

        // 最大Token数滑块
        if (this.elements.maxTokens) {
            this.elements.maxTokens.addEventListener('input', () => {
                this.updateMaxTokensDisplay();
            });
        }

        // 重置按钮
        if (this.elements.resetBtn) {
            this.elements.resetBtn.addEventListener('click', () => {
                this.resetConfig();
            });
        }

        // 导出配置
        if (this.elements.exportConfigBtn) {
            this.elements.exportConfigBtn.addEventListener('click', () => {
                this.exportConfig();
            });
        }

        // 导入配置
        if (this.elements.importConfigBtn) {
            this.elements.importConfigBtn.addEventListener('click', () => {
                this.elements.configFileInput?.click();
            });
        }

        if (this.elements.configFileInput) {
            this.elements.configFileInput.addEventListener('change', (e) => {
                this.importConfig(e.target.files[0]);
            });
        }

        // 关闭提示
        if (this.elements.closeToastBtn) {
            this.elements.closeToastBtn.addEventListener('click', () => {
                this.hideToast();
            });
        }
    }

    /**
     * 更新模型选项
     */
    updateModelOptions() {
        if (!this.elements.apiModel || !this.elements.apiProvider) return;

        const provider = this.elements.apiProvider.value;
        const models = this.apiClient.getSupportedModels(provider);
        
        // 清空现有选项
        this.elements.apiModel.innerHTML = '';
        
        // 添加新选项
        Object.entries(models).forEach(([value, label]) => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = label;
            this.elements.apiModel.appendChild(option);
        });

        // 设置默认选项
        if (this.elements.apiModel.options.length > 0) {
            this.elements.apiModel.selectedIndex = 0;
        }
    }

    /**
     * 切换API密钥可见性
     */
    toggleApiKeyVisibility() {
        if (!this.elements.apiKey || !this.elements.toggleApiKey) return;

        const isPassword = this.elements.apiKey.type === 'password';
        this.elements.apiKey.type = isPassword ? 'text' : 'password';
        this.elements.toggleApiKey.textContent = isPassword ? '🙈' : '👁️';
    }

    /**
     * 更新最大Token数显示
     */
    updateMaxTokensDisplay() {
        if (!this.elements.maxTokens || !this.elements.maxTokensValue) return;

        this.elements.maxTokensValue.textContent = this.elements.maxTokens.value;
    }

    /**
     * 测试API连接
     */
    async testApiConnection() {
        if (!this.elements.testApiBtn || !this.elements.apiTestResult) return;

        try {
            // 更新按钮状态
            const btnText = this.elements.testApiBtn.querySelector('.btn-text');
            const btnLoading = this.elements.testApiBtn.querySelector('.btn-loading');
            
            if (btnText && btnLoading) {
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline';
            }
            this.elements.testApiBtn.disabled = true;

            // 临时更新API配置
            await this.apiClient.updateConfig({
                provider: this.elements.apiProvider?.value,
                apiKey: this.elements.apiKey?.value,
                model: this.elements.apiModel?.value
            });

            // 测试连接
            const result = await this.apiClient.testConnection();
            
            // 显示结果
            this.elements.apiTestResult.style.display = 'block';
            this.elements.apiTestResult.className = `test-result ${result.success ? 'success' : 'error'}`;
            this.elements.apiTestResult.textContent = result.message;

        } catch (error) {
            this.logger.error('API连接测试失败:', error);
            this.elements.apiTestResult.style.display = 'block';
            this.elements.apiTestResult.className = 'test-result error';
            this.elements.apiTestResult.textContent = '测试失败: ' + error.message;
        } finally {
            // 恢复按钮状态
            const btnText = this.elements.testApiBtn.querySelector('.btn-text');
            const btnLoading = this.elements.testApiBtn.querySelector('.btn-loading');
            
            if (btnText && btnLoading) {
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
            }
            this.elements.testApiBtn.disabled = false;
        }
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        try {
            // 收集表单数据
            const formData = this.collectFormData();
            
            // 验证配置
            const validation = this.validateConfig(formData);
            if (!validation.valid) {
                this.showToast('error', validation.message);
                return;
            }

            // 保存配置
            await this.config.setConfig(formData);
            
            this.showToast('success', '配置保存成功');
            this.logger.info('配置保存成功');

        } catch (error) {
            this.logger.error('保存配置失败:', error);
            this.showToast('error', '保存配置失败: ' + error.message);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const data = {};

        // API配置
        if (this.elements.apiProvider) data.apiProvider = this.elements.apiProvider.value;
        if (this.elements.apiKey) data.apiKey = this.elements.apiKey.value.trim();
        if (this.elements.apiModel) data.apiModel = this.elements.apiModel.value;

        // 分析配置
        if (this.elements.enableSummary) data.enableSummary = this.elements.enableSummary.checked;
        if (this.elements.enableImportance) data.enableImportance = this.elements.enableImportance.checked;
        if (this.elements.enableTodos) data.enableTodos = this.elements.enableTodos.checked;
        if (this.elements.enableKeyInfo) data.enableKeyInfo = this.elements.enableKeyInfo.checked;
        if (this.elements.analysisLanguage) data.analysisLanguage = this.elements.analysisLanguage.value;
        if (this.elements.maxTokens) data.maxTokens = parseInt(this.elements.maxTokens.value);

        // 界面配置
        const selectedTheme = document.querySelector('input[name="theme"]:checked');
        if (selectedTheme) data.theme = selectedTheme.value;
        if (this.elements.autoAnalysis) data.autoAnalysis = this.elements.autoAnalysis.checked;
        if (this.elements.showNotifications) data.showNotifications = this.elements.showNotifications.checked;

        // 高级配置
        if (this.elements.requestTimeout) data.requestTimeout = parseInt(this.elements.requestTimeout.value) * 1000;
        if (this.elements.retryAttempts) data.retryAttempts = parseInt(this.elements.retryAttempts.value);
        if (this.elements.enableDebugLog) data.enableDebugLog = this.elements.enableDebugLog.checked;

        return data;
    }

    /**
     * 验证配置
     */
    validateConfig(data) {
        if (!data.apiKey || data.apiKey.length < 10) {
            return { valid: false, message: 'API密钥不能为空且长度至少10个字符' };
        }

        if (!data.apiProvider) {
            return { valid: false, message: '请选择API提供商' };
        }

        if (!data.apiModel) {
            return { valid: false, message: '请选择模型' };
        }

        if (data.maxTokens < 100 || data.maxTokens > 8000) {
            return { valid: false, message: '最大Token数必须在100-8000之间' };
        }

        if (data.requestTimeout < 5000 || data.requestTimeout > 120000) {
            return { valid: false, message: '请求超时时间必须在5-120秒之间' };
        }

        return { valid: true };
    }

    /**
     * 重置配置
     */
    async resetConfig() {
        if (!confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
            return;
        }

        try {
            await this.config.resetConfig();
            await this.loadCurrentConfig();
            this.showToast('success', '配置已重置为默认值');
            this.logger.info('配置已重置');

        } catch (error) {
            this.logger.error('重置配置失败:', error);
            this.showToast('error', '重置配置失败: ' + error.message);
        }
    }

    /**
     * 导出配置
     */
    exportConfig() {
        try {
            const configData = this.config.exportConfig(false); // 不包含敏感信息
            const dataStr = JSON.stringify(configData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `ai-assistant-config-${Date.now()}.json`;
            link.click();
            
            this.showToast('success', '配置已导出');
            this.logger.info('配置导出成功');

        } catch (error) {
            this.logger.error('导出配置失败:', error);
            this.showToast('error', '导出配置失败: ' + error.message);
        }
    }

    /**
     * 导入配置
     */
    async importConfig(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const result = await this.config.importConfig(text, { overwrite: false });
            
            if (result.success) {
                await this.loadCurrentConfig();
                this.showToast('success', result.message);
                this.logger.info('配置导入成功');
            } else {
                this.showToast('error', result.message);
            }

        } catch (error) {
            this.logger.error('导入配置失败:', error);
            this.showToast('error', '导入配置失败: ' + error.message);
        }
    }

    /**
     * 显示提示
     */
    showToast(type, message) {
        if (!this.elements.configToast || !this.elements.toastIcon || !this.elements.toastMessage) return;

        const icons = {
            success: '✅',
            error: '❌',
            info: 'ℹ️'
        };

        this.elements.toastIcon.textContent = icons[type] || icons.info;
        this.elements.toastMessage.textContent = message;
        this.elements.configToast.className = `toast ${type}`;
        this.elements.configToast.style.display = 'flex';

        // 自动隐藏
        setTimeout(() => this.hideToast(), type === 'error' ? 5000 : 3000);
    }

    /**
     * 隐藏提示
     */
    hideToast() {
        if (this.elements.configToast) {
            this.elements.configToast.style.display = 'none';
        }
    }
}

// 应用程序入口
document.addEventListener('DOMContentLoaded', async () => {
    const app = new ConfigApp();
    
    // 将应用实例挂载到全局，便于调试
    window.configApp = app;
    
    try {
        await app.initialize();
    } catch (error) {
        console.error('配置应用启动失败:', error);
    }
});
