<!-- 待办事项模板 -->
<div class="todo-template">
    <div class="todo-header">
        <h4 class="todo-title">📋 待办事项</h4>
        <div class="todo-stats">
            <span class="todo-count">{{totalCount}} 项</span>
            <span class="todo-completed">已完成 {{completedCount}}</span>
        </div>
    </div>
    
    <div class="todo-content">
        <div class="todo-filters">
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="high">高优先级</button>
            <button class="filter-btn" data-filter="pending">待处理</button>
            <button class="filter-btn" data-filter="completed">已完成</button>
        </div>
        
        <div class="todo-list-container">
            <ul class="todo-list">
                {{#each todos}}
                <li class="todo-item" data-priority="{{priority}}" data-status="{{status}}">
                    <div class="todo-checkbox-wrapper">
                        <input type="checkbox" class="todo-checkbox" {{#if completed}}checked{{/if}}>
                        <span class="checkbox-custom"></span>
                    </div>
                    
                    <div class="todo-content-wrapper">
                        <div class="todo-text {{#if completed}}completed{{/if}}">
                            {{task}}
                        </div>
                        
                        <div class="todo-meta">
                            {{#if deadline}}
                            <span class="todo-deadline">
                                📅 {{deadline}}
                            </span>
                            {{/if}}
                            
                            {{#if type}}
                            <span class="todo-type">
                                🏷️ {{type}}
                            </span>
                            {{/if}}
                        </div>
                    </div>
                    
                    <div class="todo-priority-badge {{priority}}">
                        {{priorityText}}
                    </div>
                    
                    <div class="todo-actions">
                        <button class="btn-icon edit-todo" title="编辑">✏️</button>
                        <button class="btn-icon delete-todo" title="删除">🗑️</button>
                    </div>
                </li>
                {{/each}}
            </ul>
            
            <div class="todo-empty" style="display: {{#if todos}}none{{else}}block{{/if}}">
                <div class="empty-icon">📝</div>
                <div class="empty-text">暂无待办事项</div>
                <div class="empty-subtext">AI将自动从邮件中提取待办任务</div>
            </div>
        </div>
    </div>
    
    <div class="todo-actions-footer">
        <button class="btn btn-sm btn-primary add-todo">
            ➕ 添加待办
        </button>
        <button class="btn btn-sm btn-outline export-todos">
            📤 导出列表
        </button>
        <button class="btn btn-sm btn-outline clear-completed">
            🗑️ 清除已完成
        </button>
    </div>
</div>

<style>
.todo-template {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.todo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.todo-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.todo-stats {
    display: flex;
    gap: var(--spacing-sm);
    font-size: 11px;
    color: var(--text-secondary);
}

.todo-content {
    padding: var(--spacing-md);
}

.todo-filters {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    overflow-x: auto;
}

.filter-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.todo-list-container {
    max-height: 300px;
    overflow-y: auto;
}

.todo-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.todo-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.todo-item:hover {
    background: var(--bg-secondary);
}

.todo-item:last-child {
    border-bottom: none;
}

.todo-checkbox-wrapper {
    position: relative;
    margin-top: 2px;
}

.todo-checkbox {
    opacity: 0;
    position: absolute;
}

.checkbox-custom {
    display: block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.todo-checkbox:checked + .checkbox-custom {
    background: var(--success-color);
    border-color: var(--success-color);
}

.todo-checkbox:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.todo-content-wrapper {
    flex: 1;
    min-width: 0;
}

.todo-text {
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    word-wrap: break-word;
}

.todo-text.completed {
    text-decoration: line-through;
    color: var(--text-muted);
}

.todo-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.todo-deadline,
.todo-type {
    font-size: 10px;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: 2px 4px;
    border-radius: 2px;
}

.todo-priority-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 2px;
}

.todo-priority-badge.high {
    background: var(--error-color);
    color: white;
}

.todo-priority-badge.medium {
    background: var(--warning-color);
    color: white;
}

.todo-priority-badge.low {
    background: var(--success-color);
    color: white;
}

.todo-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.todo-item:hover .todo-actions {
    opacity: 1;
}

.todo-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-icon {
    font-size: 32px;
    margin-bottom: var(--spacing-sm);
}

.empty-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.empty-subtext {
    font-size: 12px;
}

.todo-actions-footer {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .todo-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .todo-actions-footer {
        flex-direction: column;
    }
    
    .btn-sm {
        width: 100%;
    }
    
    .todo-item {
        flex-wrap: wrap;
    }
    
    .todo-actions {
        opacity: 1;
        width: 100%;
        justify-content: flex-end;
    }
}
</style>
