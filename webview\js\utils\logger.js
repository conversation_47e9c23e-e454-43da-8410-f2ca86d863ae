/**
 * 日志系统模块
 * 提供统一的日志记录功能，支持不同级别的日志输出
 */

/**
 * 日志级别枚举
 */
const LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3
};

/**
 * 日志级别名称映射
 */
const LOG_LEVEL_NAMES = {
    0: 'DEBUG',
    1: 'INFO',
    2: 'WARN',
    3: 'ERROR'
};

/**
 * 日志级别颜色映射
 */
const LOG_LEVEL_COLORS = {
    0: '#6b7280', // DEBUG - 灰色
    1: '#3b82f6', // INFO - 蓝色
    2: '#f59e0b', // WARN - 黄色
    3: '#ef4444'  // ERROR - 红色
};

/**
 * Logger类
 */
export class Logger {
    constructor(name = 'App', level = LOG_LEVELS.INFO) {
        this.name = name;
        this.level = level;
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数
        
        // 从localStorage读取日志级别配置
        this.loadConfig();
    }

    /**
     * 加载配置
     */
    loadConfig() {
        try {
            const config = localStorage.getItem('ai-assistant-log-config');
            if (config) {
                const parsed = JSON.parse(config);
                this.level = parsed.level !== undefined ? parsed.level : LOG_LEVELS.INFO;
                this.maxLogs = parsed.maxLogs || 1000;
            }
        } catch (error) {
            console.warn('加载日志配置失败:', error);
        }
    }

    /**
     * 保存配置
     */
    saveConfig() {
        try {
            const config = {
                level: this.level,
                maxLogs: this.maxLogs
            };
            localStorage.setItem('ai-assistant-log-config', JSON.stringify(config));
        } catch (error) {
            console.warn('保存日志配置失败:', error);
        }
    }

    /**
     * 设置日志级别
     */
    setLevel(level) {
        this.level = level;
        this.saveConfig();
    }

    /**
     * 获取当前日志级别
     */
    getLevel() {
        return this.level;
    }

    /**
     * 记录日志
     */
    log(level, message, data = null) {
        if (level < this.level) {
            return;
        }

        const timestamp = new Date().toISOString();
        const levelName = LOG_LEVEL_NAMES[level];
        const color = LOG_LEVEL_COLORS[level];

        const logEntry = {
            timestamp,
            level,
            levelName,
            name: this.name,
            message,
            data
        };

        // 添加到内存日志
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }

        // 输出到控制台
        this.outputToConsole(logEntry, color);

        // 如果是错误级别，尝试发送到错误收集服务
        if (level === LOG_LEVELS.ERROR) {
            this.reportError(logEntry);
        }
    }

    /**
     * 输出到控制台
     */
    outputToConsole(logEntry, color) {
        const { timestamp, levelName, name, message, data } = logEntry;
        const timeStr = new Date(timestamp).toLocaleTimeString();
        const prefix = `[${timeStr}] [${levelName}] [${name}]`;

        const consoleMethod = this.getConsoleMethod(logEntry.level);
        
        if (data) {
            consoleMethod(
                `%c${prefix} ${message}`,
                `color: ${color}; font-weight: bold;`,
                data
            );
        } else {
            consoleMethod(
                `%c${prefix} ${message}`,
                `color: ${color}; font-weight: bold;`
            );
        }
    }

    /**
     * 获取对应的console方法
     */
    getConsoleMethod(level) {
        switch (level) {
            case LOG_LEVELS.DEBUG:
                return console.debug;
            case LOG_LEVELS.INFO:
                return console.info;
            case LOG_LEVELS.WARN:
                return console.warn;
            case LOG_LEVELS.ERROR:
                return console.error;
            default:
                return console.log;
        }
    }

    /**
     * DEBUG级别日志
     */
    debug(message, data = null) {
        this.log(LOG_LEVELS.DEBUG, message, data);
    }

    /**
     * INFO级别日志
     */
    info(message, data = null) {
        this.log(LOG_LEVELS.INFO, message, data);
    }

    /**
     * WARN级别日志
     */
    warn(message, data = null) {
        this.log(LOG_LEVELS.WARN, message, data);
    }

    /**
     * ERROR级别日志
     */
    error(message, data = null) {
        this.log(LOG_LEVELS.ERROR, message, data);
    }

    /**
     * 获取所有日志
     */
    getLogs(level = null, limit = null) {
        let filteredLogs = this.logs;

        if (level !== null) {
            filteredLogs = filteredLogs.filter(log => log.level >= level);
        }

        if (limit) {
            filteredLogs = filteredLogs.slice(-limit);
        }

        return filteredLogs;
    }

    /**
     * 清空日志
     */
    clearLogs() {
        this.logs = [];
        this.info('日志已清空');
    }

    /**
     * 导出日志
     */
    exportLogs(format = 'json') {
        const logs = this.getLogs();
        
        if (format === 'json') {
            return JSON.stringify(logs, null, 2);
        } else if (format === 'text') {
            return logs.map(log => {
                const timeStr = new Date(log.timestamp).toLocaleString();
                const dataStr = log.data ? ` ${JSON.stringify(log.data)}` : '';
                return `[${timeStr}] [${log.levelName}] [${log.name}] ${log.message}${dataStr}`;
            }).join('\n');
        }
        
        return logs;
    }

    /**
     * 报告错误到错误收集服务
     */
    reportError(logEntry) {
        try {
            // 这里可以集成错误收集服务，如Sentry等
            // 目前只是存储到localStorage作为示例
            const errorReports = JSON.parse(localStorage.getItem('ai-assistant-error-reports') || '[]');
            errorReports.push({
                ...logEntry,
                userAgent: navigator.userAgent,
                url: window.location.href
            });
            
            // 只保留最近100个错误报告
            if (errorReports.length > 100) {
                errorReports.splice(0, errorReports.length - 100);
            }
            
            localStorage.setItem('ai-assistant-error-reports', JSON.stringify(errorReports));
        } catch (error) {
            console.warn('报告错误失败:', error);
        }
    }

    /**
     * 获取错误报告
     */
    getErrorReports() {
        try {
            return JSON.parse(localStorage.getItem('ai-assistant-error-reports') || '[]');
        } catch (error) {
            console.warn('获取错误报告失败:', error);
            return [];
        }
    }

    /**
     * 清空错误报告
     */
    clearErrorReports() {
        localStorage.removeItem('ai-assistant-error-reports');
        this.info('错误报告已清空');
    }
}

/**
 * 创建全局Logger实例
 */
export const globalLogger = new Logger('Global');

/**
 * 日志级别常量导出
 */
export { LOG_LEVELS };
