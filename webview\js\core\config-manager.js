/**
 * 配置管理模块
 * 负责管理用户配置，包括API设置、分析选项、界面偏好等
 */

import { Logger } from '../utils/logger.js';
import { StorageManager, STORAGE_TYPES } from '../utils/storage.js';

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
    // API配置
    apiProvider: 'siliconflow',
    apiKey: '',
    apiModel: 'deepseek-ai/DeepSeek-R1',
    apiBaseUrl: '',
    maxTokens: 2000,
    temperature: 0.7,
    requestTimeout: 30000,
    retryAttempts: 2,

    // 分析配置
    enableSummary: true,
    enableImportance: true,
    enableTodos: true,
    enableKeyInfo: true,
    analysisLanguage: 'zh-CN',
    autoAnalysis: false,

    // 界面配置
    theme: 'light',
    showNotifications: true,
    sidebarWidth: 350,
    compactMode: false,

    // 高级配置
    enableDebugLog: false,
    cacheResults: true,
    cacheExpiry: 3600000, // 1小时
    
    // 版本信息
    configVersion: '1.0.0',
    lastUpdated: null
};

/**
 * 配置验证规则
 */
const CONFIG_VALIDATION = {
    apiProvider: {
        type: 'string',
        enum: ['siliconflow', 'qwen', 'custom'],
        required: true
    },
    apiKey: {
        type: 'string',
        minLength: 10,
        required: true
    },
    apiModel: {
        type: 'string',
        required: true
    },
    maxTokens: {
        type: 'number',
        min: 100,
        max: 8000
    },
    temperature: {
        type: 'number',
        min: 0,
        max: 2
    },
    requestTimeout: {
        type: 'number',
        min: 5000,
        max: 120000
    },
    retryAttempts: {
        type: 'number',
        min: 0,
        max: 5
    },
    analysisLanguage: {
        type: 'string',
        enum: ['zh-CN', 'zh-TW', 'en', 'auto']
    },
    theme: {
        type: 'string',
        enum: ['light', 'dark', 'auto']
    },
    sidebarWidth: {
        type: 'number',
        min: 250,
        max: 600
    }
};

/**
 * 配置管理器类
 */
export class ConfigManager {
    constructor() {
        this.logger = new Logger('ConfigManager');
        this.storage = new StorageManager();
        this.config = { ...DEFAULT_CONFIG };
        this.listeners = new Map();
        this.isInitialized = false;
    }

    /**
     * 初始化配置管理器
     */
    async initialize() {
        try {
            this.logger.info('初始化配置管理器');
            
            // 加载配置
            await this.loadConfig();
            
            // 验证配置
            this.validateConfig();
            
            // 迁移配置（如果需要）
            await this.migrateConfig();
            
            this.isInitialized = true;
            this.logger.info('配置管理器初始化完成');
            
        } catch (error) {
            this.logger.error('配置管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            const savedConfig = await this.storage.get('config', {}, {
                type: STORAGE_TYPES.LOCAL
            });

            // 合并默认配置和保存的配置
            this.config = {
                ...DEFAULT_CONFIG,
                ...savedConfig,
                lastUpdated: savedConfig.lastUpdated || Date.now()
            };

            this.logger.info('配置加载完成', {
                provider: this.config.apiProvider,
                model: this.config.apiModel,
                hasApiKey: !!this.config.apiKey
            });

        } catch (error) {
            this.logger.error('加载配置失败:', error);
            this.config = { ...DEFAULT_CONFIG };
        }
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        try {
            this.config.lastUpdated = Date.now();
            
            await this.storage.set('config', this.config, {
                type: STORAGE_TYPES.LOCAL
            });

            this.logger.info('配置保存完成');
            
            // 触发配置变更事件
            this.emit('configChanged', this.config);
            
            return true;

        } catch (error) {
            this.logger.error('保存配置失败:', error);
            return false;
        }
    }

    /**
     * 获取配置
     */
    getConfig(key = null) {
        if (key) {
            return this.config[key];
        }
        return { ...this.config };
    }

    /**
     * 设置配置
     */
    async setConfig(key, value) {
        if (typeof key === 'object') {
            // 批量设置
            const updates = key;
            const validatedUpdates = {};

            for (const [k, v] of Object.entries(updates)) {
                if (this.validateConfigItem(k, v)) {
                    validatedUpdates[k] = v;
                }
            }

            this.config = { ...this.config, ...validatedUpdates };
        } else {
            // 单个设置
            if (this.validateConfigItem(key, value)) {
                this.config[key] = value;
            } else {
                throw new Error(`无效的配置值: ${key} = ${value}`);
            }
        }

        return await this.saveConfig();
    }

    /**
     * 重置配置
     */
    async resetConfig(keys = null) {
        if (keys) {
            // 重置指定配置项
            if (Array.isArray(keys)) {
                keys.forEach(key => {
                    if (DEFAULT_CONFIG.hasOwnProperty(key)) {
                        this.config[key] = DEFAULT_CONFIG[key];
                    }
                });
            } else {
                if (DEFAULT_CONFIG.hasOwnProperty(keys)) {
                    this.config[keys] = DEFAULT_CONFIG[keys];
                }
            }
        } else {
            // 重置所有配置
            this.config = { ...DEFAULT_CONFIG };
        }

        await this.saveConfig();
        this.logger.info('配置已重置');
    }

    /**
     * 验证配置
     */
    validateConfig() {
        const errors = [];

        for (const [key, value] of Object.entries(this.config)) {
            if (!this.validateConfigItem(key, value)) {
                errors.push(`无效的配置项: ${key} = ${value}`);
            }
        }

        if (errors.length > 0) {
            this.logger.warn('配置验证发现问题:', errors);
        }

        return errors.length === 0;
    }

    /**
     * 验证单个配置项
     */
    validateConfigItem(key, value) {
        const rule = CONFIG_VALIDATION[key];
        if (!rule) {
            return true; // 没有验证规则的配置项默认通过
        }

        // 检查必填项
        if (rule.required && (value === null || value === undefined || value === '')) {
            return false;
        }

        // 检查类型
        if (rule.type && typeof value !== rule.type) {
            return false;
        }

        // 检查枚举值
        if (rule.enum && !rule.enum.includes(value)) {
            return false;
        }

        // 检查数值范围
        if (rule.type === 'number') {
            if (rule.min !== undefined && value < rule.min) {
                return false;
            }
            if (rule.max !== undefined && value > rule.max) {
                return false;
            }
        }

        // 检查字符串长度
        if (rule.type === 'string') {
            if (rule.minLength !== undefined && value.length < rule.minLength) {
                return false;
            }
            if (rule.maxLength !== undefined && value.length > rule.maxLength) {
                return false;
            }
        }

        return true;
    }

    /**
     * 迁移配置
     */
    async migrateConfig() {
        const currentVersion = this.config.configVersion;
        const targetVersion = DEFAULT_CONFIG.configVersion;

        if (currentVersion === targetVersion) {
            return;
        }

        this.logger.info(`配置迁移: ${currentVersion} -> ${targetVersion}`);

        // 这里可以添加版本迁移逻辑
        // 例如：重命名配置项、转换数据格式等

        this.config.configVersion = targetVersion;
        await this.saveConfig();
    }

    /**
     * 导出配置
     */
    exportConfig(includeSecrets = false) {
        const exportData = { ...this.config };

        if (!includeSecrets) {
            // 移除敏感信息
            delete exportData.apiKey;
        }

        return {
            version: exportData.configVersion,
            timestamp: Date.now(),
            config: exportData
        };
    }

    /**
     * 导入配置
     */
    async importConfig(configData, options = {}) {
        try {
            const { overwrite = false, validateOnly = false } = options;

            let importData = configData;
            if (typeof configData === 'string') {
                importData = JSON.parse(configData);
            }

            // 验证导入数据格式
            if (!importData.config) {
                throw new Error('无效的配置数据格式');
            }

            const newConfig = importData.config;

            // 验证配置项
            for (const [key, value] of Object.entries(newConfig)) {
                if (!this.validateConfigItem(key, value)) {
                    throw new Error(`无效的配置项: ${key} = ${value}`);
                }
            }

            if (validateOnly) {
                return { valid: true, message: '配置验证通过' };
            }

            // 合并配置
            if (overwrite) {
                this.config = { ...DEFAULT_CONFIG, ...newConfig };
            } else {
                this.config = { ...this.config, ...newConfig };
            }

            await this.saveConfig();
            this.logger.info('配置导入完成');

            return { success: true, message: '配置导入成功' };

        } catch (error) {
            this.logger.error('配置导入失败:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * 检查配置是否完整
     */
    isConfigComplete() {
        const requiredFields = ['apiProvider', 'apiKey', 'apiModel'];
        return requiredFields.every(field => {
            const value = this.config[field];
            return value !== null && value !== undefined && value !== '';
        });
    }

    /**
     * 获取API配置
     */
    getAPIConfig() {
        return {
            provider: this.config.apiProvider,
            apiKey: this.config.apiKey,
            model: this.config.apiModel,
            baseUrl: this.config.apiBaseUrl,
            maxTokens: this.config.maxTokens,
            temperature: this.config.temperature,
            timeout: this.config.requestTimeout,
            retries: this.config.retryAttempts
        };
    }

    /**
     * 获取分析配置
     */
    getAnalysisConfig() {
        return {
            enableSummary: this.config.enableSummary,
            enableImportance: this.config.enableImportance,
            enableTodos: this.config.enableTodos,
            enableKeyInfo: this.config.enableKeyInfo,
            language: this.config.analysisLanguage,
            autoAnalysis: this.config.autoAnalysis
        };
    }

    /**
     * 获取界面配置
     */
    getUIConfig() {
        return {
            theme: this.config.theme,
            showNotifications: this.config.showNotifications,
            sidebarWidth: this.config.sidebarWidth,
            compactMode: this.config.compactMode
        };
    }

    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.logger.error(`事件回调执行失败 (${event}):`, error);
                }
            });
        }
    }
}
