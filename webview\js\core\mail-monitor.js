/**
 * 邮件监控模块
 * 负责监控邮件变化，获取邮件数据，与Coremail插件API交互
 */

import { Logger } from '../utils/logger.js';

/**
 * 邮件监控器类
 */
export class MailMonitor {
    constructor(aiAnalyzer = null, chatManager = null) {
        this.logger = new Logger('MailMonitor');
        this.aiAnalyzer = aiAnalyzer;
        this.chatManager = chatManager;

        this.currentMail = null;
        this.listeners = new Map();
        this.isMonitoring = false;
        this.pollInterval = null;
        this.pollFrequency = 3000; // 3秒轮询一次

        // 邮件缓存，用于检测新邮件
        this.mailCache = new Map();
        this.lastCheckTime = Date.now();

        // 统计信息
        this.stats = {
            newMailCount: 0,
            todayAnalysisCount: 0,
            totalAnalysisCount: 0
        };

        // 自动分析配置
        this.autoAnalysisEnabled = true;

        // Coremail插件API可用性检查
        this.cmPluginAvailable = false;
    }

    /**
     * 初始化邮件监控器
     */
    async initialize() {
        try {
            this.logger.info('初始化邮件监控器');
            
            // 检查Coremail插件API是否可用
            this.checkCMPluginAvailability();
            
            if (!this.cmPluginAvailable) {
                this.logger.warn('Coremail插件API不可用，将使用模拟数据');
            }
            
            // 开始监控
            await this.startMonitoring();
            
            this.logger.info('邮件监控器初始化完成');
            
        } catch (error) {
            this.logger.error('邮件监控器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 检查Coremail插件API可用性
     */
    checkCMPluginAvailability() {
        this.cmPluginAvailable = typeof CMPlugin !== 'undefined' && 
                                 typeof CMPlugin.getAttributes === 'function';
        
        this.logger.info('Coremail插件API可用性:', this.cmPluginAvailable);
    }

    /**
     * 开始监控邮件
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;
        this.logger.info('开始监控邮件变化');

        // 立即获取一次当前邮件
        await this.checkMailChange();

        // 设置定时轮询
        this.pollInterval = setInterval(async () => {
            try {
                await this.checkMailChange();
            } catch (error) {
                this.logger.error('邮件变化检查失败:', error);
            }
        }, this.pollFrequency);
    }

    /**
     * 停止监控邮件
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }

        this.logger.info('已停止监控邮件变化');
    }

    /**
     * 检查邮件变化
     */
    async checkMailChange() {
        try {
            const currentMailInfo = await this.getCurrentMailInfo();

            // 比较邮件是否发生变化
            if (this.hasMailChanged(currentMailInfo)) {
                this.logger.info('检测到邮件变化');
                this.currentMail = currentMailInfo;
                this.emit('mailChanged', currentMailInfo);

                // 如果启用自动分析，对新邮件进行分析
                if (this.autoAnalysisEnabled && currentMailInfo && this.aiAnalyzer) {
                    await this.autoAnalyzeMail(currentMailInfo);
                }
            }

        } catch (error) {
            this.logger.error('检查邮件变化失败:', error);
        }
    }

    /**
     * 检查新邮件
     */
    async checkNewMails() {
        try {
            // 这里可以实现检查收件箱新邮件的逻辑
            // 由于Coremail插件API限制，这里使用模拟实现
            if (Math.random() < 0.1) { // 10%概率模拟收到新邮件
                const newMail = this.generateMockNewMail();
                await this.handleNewMail(newMail);
            }

        } catch (error) {
            this.logger.error('检查新邮件失败:', error);
        }
    }

    /**
     * 处理新邮件
     */
    async handleNewMail(mailInfo) {
        try {
            this.logger.info('收到新邮件:', mailInfo.subject);
            this.stats.newMailCount++;

            // 缓存邮件信息
            this.mailCache.set(mailInfo.id, mailInfo);

            // 触发新邮件事件
            this.emit('newMail', mailInfo);

            // 自动分析新邮件
            if (this.autoAnalysisEnabled && this.aiAnalyzer) {
                await this.autoAnalyzeMail(mailInfo);
            }

        } catch (error) {
            this.logger.error('处理新邮件失败:', error);
        }
    }

    /**
     * 自动分析邮件
     */
    async autoAnalyzeMail(mailInfo) {
        try {
            this.logger.info('开始自动分析邮件:', mailInfo.subject);

            // 使用AI分析器分析邮件
            const analysisResults = await this.aiAnalyzer.analyzeEmail(mailInfo);

            // 更新统计
            this.stats.todayAnalysisCount++;
            this.stats.totalAnalysisCount++;

            // 将分析结果添加到邮件知识库
            if (this.chatManager) {
                this.chatManager.addMailToKnowledgeBase(mailInfo, analysisResults);
            }

            // 触发分析完成事件
            this.emit('mailAnalyzed', {
                mailInfo: mailInfo,
                analysisResults: analysisResults
            });

            // 如果是重要邮件，发送通知
            if (analysisResults.importance && analysisResults.importance.score >= 8) {
                this.emit('importantMailDetected', {
                    mailInfo: mailInfo,
                    importance: analysisResults.importance
                });
            }

            this.logger.info('邮件自动分析完成');

        } catch (error) {
            this.logger.error('自动分析邮件失败:', error);
        }
    }

    /**
     * 获取当前邮件信息
     */
    async getCurrentMailInfo() {
        if (this.cmPluginAvailable) {
            return await this.getCMPluginMailInfo();
        } else {
            return this.getMockMailInfo();
        }
    }

    /**
     * 通过Coremail插件API获取邮件信息
     */
    async getCMPluginMailInfo() {
        return new Promise((resolve, reject) => {
            try {
                CMPlugin.getAttributes({
                    "sid": "",
                    "mid": "",
                    "mailinfo": "",
                    "version": "",
                    "locale": ""
                }, (result) => {
                    try {
                        if (result && result.mailinfo) {
                            const mailInfo = this.parseMailInfo(result);
                            resolve(mailInfo);
                        } else {
                            resolve(null);
                        }
                    } catch (error) {
                        this.logger.error('解析邮件信息失败:', error);
                        reject(error);
                    }
                });
            } catch (error) {
                this.logger.error('调用CMPlugin.getAttributes失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 解析Coremail插件返回的邮件信息
     */
    parseMailInfo(result) {
        const mailinfo = result.mailinfo;
        
        if (!mailinfo || !mailinfo.ExtKey) {
            return null;
        }

        return {
            id: mailinfo.ExtKey,
            subject: mailinfo.Subject || '无主题',
            sender: this.parseSenderInfo(mailinfo.SenderDecode),
            recipients: this.parseRecipients(mailinfo.ToDecode),
            ccRecipients: this.parseRecipients(mailinfo.CcDecode),
            bccRecipients: this.parseRecipients(mailinfo.BccDecode),
            content: mailinfo.Summary || '',
            size: mailinfo.Size || 0,
            flags: mailinfo.Flags || 0,
            mailTime: new Date(mailinfo.MailTime * 1000),
            recTime: new Date(mailinfo.RecTime * 1000),
            folderId: mailinfo.FID || 0,
            isRead: !!(mailinfo.Flags & 1),
            isImportant: !!(mailinfo.Flags & 2),
            hasAttachments: !!(mailinfo.Flags & 4),
            sessionId: result.sid || '',
            locale: result.locale || 'zh-CN',
            version: result.version || ''
        };
    }

    /**
     * 解析发送者信息
     */
    parseSenderInfo(senderDecode) {
        if (!senderDecode) {
            return { name: '', email: '', display: '' };
        }

        return {
            name: senderDecode.Name || '',
            email: senderDecode.Email || '',
            display: senderDecode.Display || senderDecode.Email || ''
        };
    }

    /**
     * 解析收件人信息
     */
    parseRecipients(recipientsList) {
        if (!recipientsList || !Array.isArray(recipientsList)) {
            return [];
        }

        return recipientsList.map(recipient => ({
            name: recipient.Name || '',
            email: recipient.Email || '',
            display: recipient.Display || recipient.Email || ''
        }));
    }

    /**
     * 获取模拟邮件信息（用于测试）
     */
    getMockMailInfo() {
        // 模拟邮件数据，用于开发和测试
        return {
            id: 'mock_' + Date.now(),
            subject: '测试邮件 - AI助手功能演示',
            sender: {
                name: '张三',
                email: '<EMAIL>',
                display: '张三 <<EMAIL>>'
            },
            recipients: [
                {
                    name: '李四',
                    email: '<EMAIL>',
                    display: '李四 <<EMAIL>>'
                }
            ],
            ccRecipients: [],
            bccRecipients: [],
            content: `亲爱的李四，

希望这封邮件能找到你。我想和你讨论一下下周的项目会议安排。

会议详情：
- 时间：下周三（3月15日）上午10:00
- 地点：会议室A
- 议题：Q1季度总结和Q2规划

请确认你的参会时间，如果有冲突请及时告知。

另外，请准备以下材料：
1. Q1项目进度报告
2. Q2工作计划草案
3. 预算申请表

如有任何问题，请随时联系我。

谢谢！

张三`,
            size: 1024,
            flags: 8,
            mailTime: new Date(),
            recTime: new Date(),
            folderId: 1,
            isRead: false,
            isImportant: true,
            hasAttachments: false,
            sessionId: 'mock_session_id',
            locale: 'zh-CN',
            version: '1.0.0'
        };
    }

    /**
     * 检查邮件是否发生变化
     */
    hasMailChanged(newMailInfo) {
        if (!this.currentMail && !newMailInfo) {
            return false;
        }

        if (!this.currentMail && newMailInfo) {
            return true;
        }

        if (this.currentMail && !newMailInfo) {
            return true;
        }

        // 比较邮件ID
        return this.currentMail.id !== newMailInfo.id;
    }

    /**
     * 获取当前邮件
     */
    getCurrentMail() {
        return this.currentMail;
    }

    /**
     * 获取邮件正文内容
     */
    async getMailContent(mailId = null) {
        const targetMail = mailId ? await this.getMailById(mailId) : this.currentMail;
        
        if (!targetMail) {
            throw new Error('未找到指定邮件');
        }

        // 如果已有内容，直接返回
        if (targetMail.content && targetMail.content.length > 100) {
            return targetMail.content;
        }

        // 尝试获取完整邮件内容
        if (this.cmPluginAvailable) {
            return await this.getFullMailContent(targetMail.id);
        } else {
            return targetMail.content || '无法获取邮件内容';
        }
    }

    /**
     * 获取完整邮件内容
     */
    async getFullMailContent(mailId) {
        // 这里可以通过Coremail插件API获取完整邮件内容
        // 由于API限制，目前返回摘要内容
        return this.currentMail?.content || '邮件内容获取中...';
    }

    /**
     * 根据ID获取邮件
     */
    async getMailById(mailId) {
        if (this.currentMail && this.currentMail.id === mailId) {
            return this.currentMail;
        }

        // 这里可以实现根据ID获取特定邮件的逻辑
        // 目前返回当前邮件
        return this.currentMail;
    }

    /**
     * 获取邮件统计信息
     */
    getMailStats() {
        if (!this.currentMail) {
            return null;
        }

        const content = this.currentMail.content || '';
        
        return {
            wordCount: content.length,
            estimatedReadTime: Math.ceil(content.length / 200), // 假设每分钟读200字
            hasAttachments: this.currentMail.hasAttachments,
            isImportant: this.currentMail.isImportant,
            recipientCount: this.currentMail.recipients.length + 
                           this.currentMail.ccRecipients.length + 
                           this.currentMail.bccRecipients.length
        };
    }

    /**
     * 设置轮询频率
     */
    setPollFrequency(frequency) {
        this.pollFrequency = Math.max(1000, frequency); // 最小1秒
        
        if (this.isMonitoring) {
            this.stopMonitoring();
            this.startMonitoring();
        }
        
        this.logger.info(`轮询频率已设置为 ${this.pollFrequency}ms`);
    }

    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.logger.error(`事件回调执行失败 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 销毁监控器
     */
    destroy() {
        this.stopMonitoring();
        this.listeners.clear();
        this.currentMail = null;
        this.logger.info('邮件监控器已销毁');
    }

    /**
     * 生成模拟新邮件
     */
    generateMockNewMail() {
        const subjects = [
            '项目进度更新',
            '会议邀请：下周一团队会议',
            '重要：合同审批需要您的确认',
            '系统维护通知',
            '客户反馈汇总',
            '月度报告已完成',
            '紧急：服务器异常告警'
        ];

        const senders = [
            { name: '张经理', email: '<EMAIL>' },
            { name: '李总监', email: '<EMAIL>' },
            { name: '王助理', email: '<EMAIL>' },
            { name: '系统管理员', email: '<EMAIL>' }
        ];

        const contents = [
            '请查看附件中的项目进度报告，有几个关键节点需要您的关注和决策。',
            '下周一下午2点在会议室A举行团队会议，请准时参加。',
            '合同条款已经法务部门审核完毕，请您最终确认后我们可以正式签署。',
            '系统将在本周末进行例行维护，预计影响时间2小时。',
            '本月客户满意度调查结果出炉，整体评分有所提升。',
            '月度财务报告已完成，请查看相关数据分析。',
            '监控系统检测到服务器CPU使用率异常，请及时处理。'
        ];

        const randomIndex = Math.floor(Math.random() * subjects.length);
        const sender = senders[Math.floor(Math.random() * senders.length)];

        return {
            id: 'mock_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            subject: subjects[randomIndex],
            sender: {
                name: sender.name,
                email: sender.email,
                display: sender.name
            },
            content: contents[randomIndex],
            receivedDate: new Date().toISOString(),
            isRead: false,
            isImportant: Math.random() < 0.3, // 30%概率为重要邮件
            hasAttachments: Math.random() < 0.2 // 20%概率有附件
        };
    }

    /**
     * 获取监控统计信息
     */
    getStats() {
        return {
            ...this.stats,
            isMonitoring: this.isMonitoring,
            cacheSize: this.mailCache.size,
            autoAnalysisEnabled: this.autoAnalysisEnabled
        };
    }

    /**
     * 设置自动分析开关
     */
    setAutoAnalysis(enabled) {
        this.autoAnalysisEnabled = enabled;
        this.logger.info('自动分析已', enabled ? '启用' : '禁用');
    }

    /**
     * 清空邮件缓存
     */
    clearMailCache() {
        this.mailCache.clear();
        this.logger.info('邮件缓存已清空');
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            newMailCount: 0,
            todayAnalysisCount: 0,
            totalAnalysisCount: 0
        };
        this.logger.info('统计信息已重置');
    }
}
