/**
 * AI分析引擎模块
 * 负责调用大模型API进行邮件内容分析，包括摘要生成、重要性评估、待办事项提取等
 */

import { Logger } from '../utils/logger.js';
import { APIClient } from './api-client.js';
import { ConfigManager } from './config-manager.js';

/**
 * 分析提示词模板
 */
const ANALYSIS_PROMPTS = {
    summary: {
        'zh-CN': `请分析以下邮件内容，生成简洁的摘要（不超过150字）：

邮件主题：{subject}
发送者：{sender}
邮件内容：
{content}

请提供：
1. 邮件主要内容摘要
2. 关键信息点

摘要：`,
        'en': `Please analyze the following email content and generate a concise summary (no more than 150 words):

Subject: {subject}
Sender: {sender}
Content:
{content}

Please provide:
1. Main content summary
2. Key information points

Summary:`
    },
    
    importance: {
        'zh-CN': `请评估以下邮件的重要性程度（1-10分，10分最重要）：

邮件主题：{subject}
发送者：{sender}
邮件内容：
{content}

评估标准：
- 紧急程度
- 业务重要性
- 需要回复的程度
- 涉及的人员层级

请以JSON格式返回：
{
  "score": 数字(1-10),
  "level": "低/中/高",
  "reason": "评估理由"
}`,
        'en': `Please evaluate the importance level of the following email (1-10 scale, 10 being most important):

Subject: {subject}
Sender: {sender}
Content:
{content}

Evaluation criteria:
- Urgency level
- Business importance
- Need for response
- Personnel level involved

Please return in JSON format:
{
  "score": number(1-10),
  "level": "low/medium/high",
  "reason": "evaluation reason"
}`
    },
    
    todos: {
        'zh-CN': `请从以下邮件中提取待办事项：

邮件主题：{subject}
发送者：{sender}
邮件内容：
{content}

请识别：
1. 明确的任务要求
2. 需要回复的问题
3. 会议或截止时间
4. 需要准备的材料

请以JSON格式返回：
{
  "todos": [
    {
      "task": "任务描述",
      "priority": "高/中/低",
      "deadline": "截止时间（如有）",
      "type": "任务类型"
    }
  ]
}`,
        'en': `Please extract action items from the following email:

Subject: {subject}
Sender: {sender}
Content:
{content}

Please identify:
1. Clear task requirements
2. Questions that need responses
3. Meetings or deadlines
4. Materials to prepare

Please return in JSON format:
{
  "todos": [
    {
      "task": "task description",
      "priority": "high/medium/low",
      "deadline": "deadline (if any)",
      "type": "task type"
    }
  ]
}`
    },
    
    keyInfo: {
        'zh-CN': `请从以下邮件中提取关键信息：

邮件主题：{subject}
发送者：{sender}
邮件内容：
{content}

请提取：
1. 时间信息（会议时间、截止日期等）
2. 联系人信息
3. 地点信息
4. 数字信息（金额、数量等）
5. 主题分类

请以JSON格式返回：
{
  "timeInfo": "时间相关信息",
  "contacts": ["联系人列表"],
  "locations": ["地点信息"],
  "numbers": ["重要数字"],
  "category": "邮件分类"
}`,
        'en': `Please extract key information from the following email:

Subject: {subject}
Sender: {sender}
Content:
{content}

Please extract:
1. Time information (meeting times, deadlines, etc.)
2. Contact information
3. Location information
4. Numerical information (amounts, quantities, etc.)
5. Topic classification

Please return in JSON format:
{
  "timeInfo": "time-related information",
  "contacts": ["contact list"],
  "locations": ["location information"],
  "numbers": ["important numbers"],
  "category": "email category"
}`
    }
};

/**
 * AI分析器类
 */
export class AIAnalyzer {
    constructor() {
        this.logger = new Logger('AIAnalyzer');
        this.apiClient = new APIClient();
        this.config = new ConfigManager();
        this.listeners = new Map();
        this.analysisCache = new Map();
        this.isInitialized = false;
    }

    /**
     * 初始化AI分析器
     */
    async initialize() {
        try {
            this.logger.info('初始化AI分析器');
            
            await this.apiClient.initialize();
            await this.config.initialize();
            
            this.isInitialized = true;
            this.logger.info('AI分析器初始化完成');
            
        } catch (error) {
            this.logger.error('AI分析器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 分析邮件
     */
    async analyzeEmail(mailInfo) {
        if (!this.isInitialized) {
            throw new Error('AI分析器未初始化');
        }

        if (!mailInfo) {
            throw new Error('邮件信息不能为空');
        }

        try {
            this.logger.info('开始分析邮件:', mailInfo.id);
            
            // 检查缓存
            const cacheKey = this.getCacheKey(mailInfo);
            if (this.analysisCache.has(cacheKey)) {
                this.logger.info('使用缓存的分析结果');
                return this.analysisCache.get(cacheKey);
            }

            const analysisConfig = this.config.getAnalysisConfig();
            const results = {};

            // 并行执行各种分析
            const analysisPromises = [];

            if (analysisConfig.enableSummary) {
                analysisPromises.push(
                    this.generateSummary(mailInfo, analysisConfig.language)
                        .then(result => { results.summary = result; })
                        .catch(error => { 
                            this.logger.error('摘要生成失败:', error);
                            results.summary = { error: error.message };
                        })
                );
            }

            if (analysisConfig.enableImportance) {
                analysisPromises.push(
                    this.evaluateImportance(mailInfo, analysisConfig.language)
                        .then(result => { results.importance = result; })
                        .catch(error => { 
                            this.logger.error('重要性评估失败:', error);
                            results.importance = { error: error.message };
                        })
                );
            }

            if (analysisConfig.enableTodos) {
                analysisPromises.push(
                    this.extractTodos(mailInfo, analysisConfig.language)
                        .then(result => { results.todos = result; })
                        .catch(error => { 
                            this.logger.error('待办事项提取失败:', error);
                            results.todos = { error: error.message };
                        })
                );
            }

            if (analysisConfig.enableKeyInfo) {
                analysisPromises.push(
                    this.extractKeyInfo(mailInfo, analysisConfig.language)
                        .then(result => { results.keyInfo = result; })
                        .catch(error => { 
                            this.logger.error('关键信息提取失败:', error);
                            results.keyInfo = { error: error.message };
                        })
                );
            }

            // 等待所有分析完成
            await Promise.all(analysisPromises);

            // 添加元数据
            results.metadata = {
                mailId: mailInfo.id,
                analyzedAt: new Date().toISOString(),
                language: analysisConfig.language,
                model: this.apiClient.getConfig().model
            };

            // 缓存结果
            this.cacheResults(cacheKey, results);

            this.logger.info('邮件分析完成');
            this.emit('analysisComplete', results);

            return results;

        } catch (error) {
            this.logger.error('邮件分析失败:', error);
            this.emit('analysisError', error);
            throw error;
        }
    }

    /**
     * 生成邮件摘要
     */
    async generateSummary(mailInfo, language = 'zh-CN') {
        const prompt = this.buildPrompt('summary', mailInfo, language);
        
        try {
            const response = await this.apiClient.sendChatRequest([
                { role: 'user', content: prompt }
            ], {
                maxTokens: 300,
                temperature: 0.3
            });

            return {
                content: response.content.trim(),
                wordCount: response.content.length,
                model: response.model
            };

        } catch (error) {
            this.logger.error('摘要生成失败:', error);
            throw new Error('摘要生成失败: ' + error.message);
        }
    }

    /**
     * 评估邮件重要性
     */
    async evaluateImportance(mailInfo, language = 'zh-CN') {
        const prompt = this.buildPrompt('importance', mailInfo, language);
        
        try {
            const response = await this.apiClient.sendChatRequest([
                { role: 'user', content: prompt }
            ], {
                maxTokens: 200,
                temperature: 0.1
            });

            // 尝试解析JSON响应
            const jsonMatch = response.content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    score: Math.max(1, Math.min(10, result.score || 5)),
                    level: result.level || 'medium',
                    reason: result.reason || '无法确定重要性原因',
                    model: response.model
                };
            } else {
                // 如果无法解析JSON，使用简单的关键词分析
                return this.fallbackImportanceEvaluation(mailInfo);
            }

        } catch (error) {
            this.logger.error('重要性评估失败:', error);
            return this.fallbackImportanceEvaluation(mailInfo);
        }
    }

    /**
     * 提取待办事项
     */
    async extractTodos(mailInfo, language = 'zh-CN') {
        const prompt = this.buildPrompt('todos', mailInfo, language);
        
        try {
            const response = await this.apiClient.sendChatRequest([
                { role: 'user', content: prompt }
            ], {
                maxTokens: 400,
                temperature: 0.2
            });

            // 尝试解析JSON响应
            const jsonMatch = response.content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    todos: result.todos || [],
                    count: (result.todos || []).length,
                    model: response.model
                };
            } else {
                // 如果无法解析JSON，使用简单的关键词提取
                return this.fallbackTodoExtraction(mailInfo);
            }

        } catch (error) {
            this.logger.error('待办事项提取失败:', error);
            return this.fallbackTodoExtraction(mailInfo);
        }
    }

    /**
     * 提取关键信息
     */
    async extractKeyInfo(mailInfo, language = 'zh-CN') {
        const prompt = this.buildPrompt('keyInfo', mailInfo, language);
        
        try {
            const response = await this.apiClient.sendChatRequest([
                { role: 'user', content: prompt }
            ], {
                maxTokens: 300,
                temperature: 0.1
            });

            // 尝试解析JSON响应
            const jsonMatch = response.content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    timeInfo: result.timeInfo || '',
                    contacts: result.contacts || [],
                    locations: result.locations || [],
                    numbers: result.numbers || [],
                    category: result.category || '其他',
                    model: response.model
                };
            } else {
                // 如果无法解析JSON，使用简单的信息提取
                return this.fallbackKeyInfoExtraction(mailInfo);
            }

        } catch (error) {
            this.logger.error('关键信息提取失败:', error);
            return this.fallbackKeyInfoExtraction(mailInfo);
        }
    }

    /**
     * 构建分析提示词
     */
    buildPrompt(type, mailInfo, language) {
        const template = ANALYSIS_PROMPTS[type][language] || ANALYSIS_PROMPTS[type]['zh-CN'];
        
        return template
            .replace('{subject}', mailInfo.subject || '无主题')
            .replace('{sender}', mailInfo.sender?.display || '未知发送者')
            .replace('{content}', this.truncateContent(mailInfo.content || '无内容'));
    }

    /**
     * 截断邮件内容以适应token限制
     */
    truncateContent(content, maxLength = 2000) {
        if (content.length <= maxLength) {
            return content;
        }
        
        return content.substring(0, maxLength) + '...[内容已截断]';
    }

    /**
     * 备用重要性评估（基于关键词）
     */
    fallbackImportanceEvaluation(mailInfo) {
        const urgentKeywords = ['紧急', '急', '立即', '马上', 'urgent', 'asap', 'immediately'];
        const importantKeywords = ['重要', '关键', '必须', 'important', 'critical', 'must'];
        
        const content = (mailInfo.subject + ' ' + mailInfo.content).toLowerCase();
        let score = 5;
        
        if (urgentKeywords.some(keyword => content.includes(keyword))) {
            score += 3;
        }
        
        if (importantKeywords.some(keyword => content.includes(keyword))) {
            score += 2;
        }
        
        if (mailInfo.isImportant) {
            score += 1;
        }
        
        score = Math.max(1, Math.min(10, score));
        
        return {
            score: score,
            level: score >= 8 ? 'high' : score >= 5 ? 'medium' : 'low',
            reason: '基于关键词分析的重要性评估',
            model: 'fallback'
        };
    }

    /**
     * 备用待办事项提取（基于关键词）
     */
    fallbackTodoExtraction(mailInfo) {
        const actionKeywords = ['请', '需要', '要求', '完成', '准备', 'please', 'need', 'require', 'complete'];
        const content = mailInfo.content || '';
        const todos = [];
        
        const sentences = content.split(/[。！？.!?]/);
        
        sentences.forEach(sentence => {
            if (actionKeywords.some(keyword => sentence.includes(keyword))) {
                todos.push({
                    task: sentence.trim(),
                    priority: 'medium',
                    deadline: null,
                    type: 'general'
                });
            }
        });
        
        return {
            todos: todos.slice(0, 5), // 最多返回5个
            count: todos.length,
            model: 'fallback'
        };
    }

    /**
     * 备用关键信息提取
     */
    fallbackKeyInfoExtraction(mailInfo) {
        const content = mailInfo.content || '';
        
        // 简单的时间提取
        const timeRegex = /(\d{1,2}[月\/\-]\d{1,2}[日]?|\d{4}[年\/\-]\d{1,2}[月\/\-]\d{1,2}[日]?)/g;
        const timeMatches = content.match(timeRegex) || [];
        
        // 简单的数字提取
        const numberRegex = /\d+(?:\.\d+)?[万千百十]?[元人次个]/g;
        const numberMatches = content.match(numberRegex) || [];
        
        return {
            timeInfo: timeMatches.join(', '),
            contacts: [mailInfo.sender?.name].filter(Boolean),
            locations: [],
            numbers: numberMatches,
            category: '其他',
            model: 'fallback'
        };
    }

    /**
     * 生成缓存键
     */
    getCacheKey(mailInfo) {
        return `${mailInfo.id}_${mailInfo.subject}_${Date.now()}`;
    }

    /**
     * 缓存分析结果
     */
    cacheResults(key, results) {
        const config = this.config.getConfig();
        if (config.cacheResults) {
            this.analysisCache.set(key, results);
            
            // 设置缓存过期
            setTimeout(() => {
                this.analysisCache.delete(key);
            }, config.cacheExpiry || 3600000);
        }
    }

    /**
     * 清空缓存
     */
    clearCache() {
        this.analysisCache.clear();
        this.logger.info('分析缓存已清空');
    }

    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.logger.error(`事件回调执行失败 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 获取分析统计信息
     */
    getAnalysisStats() {
        return {
            cacheSize: this.analysisCache.size,
            isInitialized: this.isInitialized,
            apiClientConfig: this.apiClient.getConfig(),
            supportedLanguages: Object.keys(ANALYSIS_PROMPTS.summary)
        };
    }

    /**
     * 测试分析功能
     */
    async testAnalysis() {
        const testMail = {
            id: 'test_' + Date.now(),
            subject: '测试邮件 - 会议安排',
            sender: { name: '测试用户', email: '<EMAIL>', display: '测试用户' },
            content: '请确认明天下午2点的项目会议，需要准备季度报告。'
        };

        try {
            const results = await this.analyzeEmail(testMail);
            this.logger.info('分析功能测试成功');
            return { success: true, results };
        } catch (error) {
            this.logger.error('分析功能测试失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 销毁分析器
     */
    destroy() {
        this.clearCache();
        this.listeners.clear();
        this.isInitialized = false;
        this.logger.info('AI分析器已销毁');
    }
}
