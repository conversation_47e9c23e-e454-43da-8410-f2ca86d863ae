/**
 * AI邮件助手 - 主入口文件
 * 负责初始化应用程序和协调各个模块
 */

import { Logger } from './utils/logger.js';
import { StorageManager } from './utils/storage.js';
import { ConfigManager } from './core/config-manager.js';
import { MailMonitor } from './core/mail-monitor.js';
import { AIAnalyzer } from './core/ai-analyzer.js';
import { ChatManager } from './core/chat-manager.js';
import { SidebarUI } from './ui/sidebar.js';

class AIEmailAssistant {
    constructor() {
        this.logger = new Logger('AIEmailAssistant');
        this.storage = new StorageManager();
        this.config = new ConfigManager();

        // 初始化AI分析器
        this.aiAnalyzer = new AIAnalyzer();

        // 初始化对话管理器
        this.chatManager = new ChatManager(null, null, this.aiAnalyzer);

        // 初始化邮件监控器（传入AI分析器和对话管理器）
        this.mailMonitor = new MailMonitor(this.aiAnalyzer, this.chatManager);

        // 初始化UI
        this.ui = new SidebarUI();

        this.isInitialized = false;
        this.currentMail = null;
        this.analysisResults = null;

        this.logger.info('AI邮件助手初始化开始');
    }

    /**
     * 初始化应用程序
     */
    async initialize() {
        try {
            this.logger.info('开始初始化应用程序');
            
            // 检查Coremail插件API是否可用
            if (typeof CMPlugin === 'undefined') {
                throw new Error('Coremail插件API不可用');
            }

            // 初始化各个模块
            await this.config.initialize();
            await this.aiAnalyzer.initialize();

            // 设置对话管理器的依赖
            this.chatManager.apiClient = this.aiAnalyzer.apiClient;
            this.chatManager.mailMonitor = this.mailMonitor;

            // 初始化对话管理器
            await this.chatManager.initialize();

            await this.mailMonitor.initialize();
            await this.ui.initialize();

            // 设置事件监听器
            this.setupEventListeners();

            // 检查配置状态
            await this.checkConfiguration();

            // 获取当前邮件信息
            await this.loadCurrentMail();

            this.isInitialized = true;
            this.logger.info('应用程序初始化完成');
            
            // 更新UI状态
            this.ui.updateConnectionStatus('已连接');
            
        } catch (error) {
            this.logger.error('应用程序初始化失败:', error);
            this.ui.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        this.logger.info('开始设置事件监听器');

        // UI事件监听 - 添加更详细的日志和错误处理
        const refreshBtn = document.getElementById('refreshBtn');
        const configBtn = document.getElementById('configBtn');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.logger.info('刷新按钮被点击');
                this.handleRefresh();
            });
            this.logger.info('刷新按钮事件监听器已设置');
        } else {
            this.logger.error('找不到刷新按钮元素 (refreshBtn)');
        }

        if (configBtn) {
            configBtn.addEventListener('click', () => {
                this.logger.info('设置按钮被点击');
                this.handleOpenConfig();
            });
            this.logger.info('设置按钮事件监听器已设置');
        } else {
            this.logger.error('找不到设置按钮元素 (configBtn)');
        }

        document.getElementById('analyzeBtn')?.addEventListener('click', () => {
            this.handleAnalyze();
        });

        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.handleExport();
        });

        document.getElementById('clearChatBtn')?.addEventListener('click', () => {
            this.handleClearChat();
        });

        // 对话输入事件
        document.getElementById('sendBtn')?.addEventListener('click', () => {
            this.handleSendMessage();
        });

        document.getElementById('chatInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSendMessage();
            }
        });

        // 快速操作按钮事件
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const question = e.target.dataset.question;
                if (question) {
                    this.handleQuickQuestion(question);
                }
            });
        });

        // 邮件监控事件
        this.mailMonitor.on('mailChanged', (mailInfo) => {
            this.handleMailChanged(mailInfo);
        });

        this.mailMonitor.on('newMail', (mailInfo) => {
            this.handleNewMail(mailInfo);
        });

        this.mailMonitor.on('mailAnalyzed', (data) => {
            this.handleMailAnalyzed(data);
        });

        this.mailMonitor.on('importantMailDetected', (data) => {
            this.handleImportantMailDetected(data);
        });

        // AI分析事件
        this.aiAnalyzer.on('analysisComplete', (results) => {
            this.handleAnalysisComplete(results);
        });

        this.aiAnalyzer.on('analysisError', (error) => {
            this.handleAnalysisError(error);
        });

        // 配置变更事件
        this.config.on('configChanged', () => {
            this.handleConfigChanged();
        });

        this.logger.info('事件监听器设置完成');
    }

    /**
     * 检查配置状态
     */
    async checkConfiguration() {
        const config = await this.config.getConfig();
        
        if (!config.apiKey) {
            this.ui.showWarning('请先配置API密钥');
            this.ui.updateAnalysisStatus('需要配置');
            return false;
        }

        if (!config.apiProvider) {
            this.ui.showWarning('请选择API提供商');
            this.ui.updateAnalysisStatus('需要配置');
            return false;
        }

        this.ui.updateAnalysisStatus('就绪');
        return true;
    }

    /**
     * 加载当前邮件信息
     */
    async loadCurrentMail() {
        try {
            const mailInfo = await this.mailMonitor.getCurrentMail();
            if (mailInfo) {
                this.currentMail = mailInfo;
                this.ui.updateCurrentMail(mailInfo);
                
                // 如果启用了自动分析，则开始分析
                const config = await this.config.getConfig();
                if (config.autoAnalysis) {
                    await this.startAnalysis();
                }
            } else {
                this.ui.updateCurrentMail(null);
            }
        } catch (error) {
            this.logger.error('加载当前邮件失败:', error);
            this.ui.showError('无法获取邮件信息');
        }
    }

    /**
     * 开始分析邮件
     */
    async startAnalysis() {
        if (!this.currentMail) {
            this.ui.showWarning('请先选择一封邮件');
            return;
        }

        const configValid = await this.checkConfiguration();
        if (!configValid) {
            return;
        }

        try {
            this.ui.showLoading(true);
            this.ui.updateAnalysisStatus('分析中');
            
            const results = await this.aiAnalyzer.analyzeEmail(this.currentMail);
            this.analysisResults = results;
            
            this.ui.updateAnalysisResults(results);
            this.ui.updateAnalysisStatus('完成');
            this.ui.showSuccess('邮件分析完成');
            
        } catch (error) {
            this.logger.error('邮件分析失败:', error);
            this.ui.showError('分析失败: ' + error.message);
            this.ui.updateAnalysisStatus('失败');
        } finally {
            this.ui.showLoading(false);
        }
    }

    /**
     * 处理刷新操作
     */
    async handleRefresh() {
        this.logger.info('用户触发刷新操作');
        try {
            this.ui.showLoading(true);
            this.ui.updateAnalysisStatus('刷新中');

            // 重新加载当前邮件
            await this.loadCurrentMail();

            // 重新检查配置
            await this.checkConfiguration();

            this.ui.showSuccess('刷新完成');
            this.logger.info('刷新操作完成');

        } catch (error) {
            this.logger.error('刷新操作失败:', error);
            this.ui.showError('刷新失败: ' + error.message);
        } finally {
            this.ui.showLoading(false);
        }
    }

    /**
     * 处理打开配置
     */
    handleOpenConfig() {
        this.logger.info('用户打开配置页面');
        try {
            // 优先使用相对路径打开配置页面
            const configUrl = 'config.html';

            // 尝试在新窗口中打开配置页面
            const configWindow = window.open(configUrl, 'ai_assistant_config',
                'width=900,height=700,scrollbars=yes,resizable=yes,status=no,toolbar=no,menubar=no');

            if (!configWindow) {
                // 如果弹窗被阻止，尝试在当前窗口打开
                this.logger.warn('弹窗被阻止，尝试在当前窗口打开配置');
                window.location.href = configUrl;
            } else {
                configWindow.focus();
            }

        } catch (error) {
            this.logger.error('打开配置页面失败:', error);
            this.ui.showError('无法打开配置页面: ' + error.message);
        }
    }

    /**
     * 处理分析操作
     */
    async handleAnalyze() {
        this.logger.info('用户触发分析操作');
        await this.startAnalysis();
    }

    /**
     * 处理导出操作
     */
    handleExport() {
        if (!this.analysisResults) {
            this.ui.showWarning('没有可导出的分析结果');
            return;
        }

        try {
            const exportData = {
                timestamp: new Date().toISOString(),
                mailInfo: this.currentMail,
                analysisResults: this.analysisResults
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `email-analysis-${Date.now()}.json`;
            link.click();
            
            this.ui.showSuccess('分析结果已导出');
            this.logger.info('分析结果导出成功');
            
        } catch (error) {
            this.logger.error('导出失败:', error);
            this.ui.showError('导出失败: ' + error.message);
        }
    }

    /**
     * 处理邮件变更事件
     */
    async handleMailChanged(mailInfo) {
        this.logger.info('检测到邮件变更');
        this.currentMail = mailInfo;
        this.ui.updateCurrentMail(mailInfo);
        
        // 清除之前的分析结果
        this.analysisResults = null;
        this.ui.clearAnalysisResults();
        
        // 如果启用了自动分析，则开始分析
        const config = await this.config.getConfig();
        if (config.autoAnalysis) {
            await this.startAnalysis();
        }
    }

    /**
     * 处理分析完成事件
     */
    handleAnalysisComplete(results) {
        this.logger.info('AI分析完成');
        this.analysisResults = results;
        this.ui.updateAnalysisResults(results);
        this.ui.updateAnalysisStatus('完成');
    }

    /**
     * 处理分析错误事件
     */
    handleAnalysisError(error) {
        this.logger.error('AI分析出错:', error);
        this.ui.showError('分析失败: ' + error.message);
        this.ui.updateAnalysisStatus('失败');
    }

    /**
     * 处理发送消息
     */
    async handleSendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();

        if (!message) {
            return;
        }

        try {
            // 清空输入框
            chatInput.value = '';

            // 显示用户消息
            this.ui.addChatMessage('user', message);

            // 显示加载状态
            this.ui.showChatLoading(true);

            // 处理用户消息
            const response = await this.chatManager.handleUserMessage(message);

            // 显示AI回复
            this.ui.addChatMessage('assistant', response);

        } catch (error) {
            this.logger.error('处理用户消息失败:', error);
            this.ui.addChatMessage('assistant', {
                type: 'error',
                content: '抱歉，处理您的消息时出现了问题，请稍后重试。'
            });
        } finally {
            this.ui.showChatLoading(false);
        }
    }

    /**
     * 处理快速问题
     */
    async handleQuickQuestion(question) {
        const chatInput = document.getElementById('chatInput');
        chatInput.value = question;
        await this.handleSendMessage();
    }

    /**
     * 处理清空对话
     */
    handleClearChat() {
        if (confirm('确定要清空所有对话记录吗？')) {
            this.chatManager.clearChatHistory();
            this.ui.clearChatMessages();
            this.ui.showSuccess('对话记录已清空');
        }
    }

    /**
     * 处理新邮件事件
     */
    async handleNewMail(mailInfo) {
        this.logger.info('收到新邮件通知:', mailInfo.subject);

        // 更新统计信息
        this.updateMonitorStats();

        // 显示新邮件通知
        this.ui.showInfo(`收到新邮件：${mailInfo.subject}`);

        // 在对话中添加新邮件通知
        this.ui.addChatMessage('assistant', {
            type: 'notification',
            content: `📧 收到新邮件：**${mailInfo.subject}**\n发送者：${mailInfo.sender.display}\n\n正在自动分析中...`
        });
    }

    /**
     * 处理邮件分析完成事件
     */
    async handleMailAnalyzed(data) {
        const { mailInfo, analysisResults } = data;
        this.logger.info('邮件分析完成:', mailInfo.subject);

        // 更新统计信息
        this.updateMonitorStats();

        // 在对话中显示分析结果
        let content = `✅ 邮件分析完成：**${mailInfo.subject}**\n\n`;

        if (analysisResults.summary) {
            content += `📄 **摘要**：${analysisResults.summary.content}\n\n`;
        }

        if (analysisResults.importance) {
            const level = analysisResults.importance.score >= 8 ? '高' :
                         analysisResults.importance.score >= 6 ? '中' : '低';
            content += `⭐ **重要性**：${analysisResults.importance.score}/10 (${level})\n\n`;
        }

        if (analysisResults.todos && analysisResults.todos.todos.length > 0) {
            content += `📋 **待办事项**：\n`;
            analysisResults.todos.todos.forEach(todo => {
                content += `• ${todo.task}\n`;
            });
        }

        this.ui.addChatMessage('assistant', {
            type: 'analysis',
            content: content,
            data: analysisResults
        });
    }

    /**
     * 处理重要邮件检测事件
     */
    async handleImportantMailDetected(data) {
        const { mailInfo, importance } = data;
        this.logger.info('检测到重要邮件:', mailInfo.subject);

        // 显示重要邮件警告
        this.ui.showWarning(`检测到重要邮件：${mailInfo.subject}`);

        // 在对话中添加重要邮件提醒
        this.ui.addChatMessage('assistant', {
            type: 'alert',
            content: `🚨 **重要邮件提醒**\n\n邮件：**${mailInfo.subject}**\n重要性：${importance.score}/10\n原因：${importance.reason}\n\n建议您及时查看和处理。`
        });
    }

    /**
     * 更新监控统计信息
     */
    updateMonitorStats() {
        const stats = this.mailMonitor.getStats();

        // 更新UI中的统计信息
        const newMailCountEl = document.getElementById('newMailCount');
        const todayAnalysisCountEl = document.getElementById('todayAnalysisCount');
        const monitorStatusEl = document.getElementById('monitorStatus');

        if (newMailCountEl) {
            newMailCountEl.textContent = stats.newMailCount;
        }

        if (todayAnalysisCountEl) {
            todayAnalysisCountEl.textContent = stats.todayAnalysisCount;
        }

        if (monitorStatusEl) {
            monitorStatusEl.textContent = stats.isMonitoring ? '监控中' : '已停止';
            monitorStatusEl.className = stats.isMonitoring ? 'status-value connected' : 'status-value';
        }
    }

    /**
     * 处理配置变更事件
     */
    async handleConfigChanged() {
        this.logger.info('配置已更新');
        await this.checkConfiguration();

        // 重新初始化AI分析器
        await this.aiAnalyzer.initialize();
    }
}

// 备用事件绑定函数 - 在主应用初始化失败时使用
function setupFallbackEventListeners() {
    console.log('设置备用事件监听器');

    const refreshBtn = document.getElementById('refreshBtn');
    const configBtn = document.getElementById('configBtn');

    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            console.log('备用刷新按钮处理函数被调用');
            alert('刷新功能暂时不可用，请检查应用初始化状态');
        });
    }

    if (configBtn) {
        configBtn.addEventListener('click', () => {
            console.log('备用设置按钮处理函数被调用');
            // 尝试直接打开配置页面
            try {
                const configUrl = 'config.html';
                const configWindow = window.open(configUrl, 'ai_assistant_config',
                    'width=900,height=700,scrollbars=yes,resizable=yes,status=no,toolbar=no,menubar=no');

                if (!configWindow) {
                    window.location.href = configUrl;
                }
            } catch (error) {
                console.error('打开配置页面失败:', error);
                alert('无法打开配置页面: ' + error.message);
            }
        });
    }
}

// 应用程序入口
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM加载完成，开始初始化应用');

    const app = new AIEmailAssistant();

    // 将应用实例挂载到全局，便于调试
    window.aiAssistant = app;

    try {
        await app.initialize();
        console.log('应用程序初始化成功');
    } catch (error) {
        console.error('应用程序启动失败:', error);

        // 如果主应用初始化失败，设置备用事件监听器
        console.log('设置备用事件监听器以确保基本功能可用');
        setupFallbackEventListeners();

        // 显示错误信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #ff4444;
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 9999;
            max-width: 300px;
        `;
        errorDiv.innerHTML = `
            <strong>应用初始化失败</strong><br>
            ${error.message}<br>
            <small>基本功能可能受限</small>
        `;
        document.body.appendChild(errorDiv);

        // 5秒后自动隐藏错误提示
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }
});

// 导出应用类供其他模块使用
export { AIEmailAssistant };
