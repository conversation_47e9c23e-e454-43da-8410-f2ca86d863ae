/**
 * 对话管理器
 * 负责处理用户与AI助手的对话交互
 */

import { Logger } from '../utils/logger.js';
import { globalErrorHandler } from '../utils/error-handler.js';

export class ChatManager {
    constructor(apiClient, mailMonitor, aiAnalyzer) {
        this.apiClient = apiClient;
        this.mailMonitor = mailMonitor;
        this.aiAnalyzer = aiAnalyzer;
        this.logger = new Logger('ChatManager');
        
        // 对话历史
        this.chatHistory = [];
        
        // 邮件知识库
        this.mailKnowledgeBase = new Map();
        
        // 意图识别模板
        this.intentPatterns = {
            // 查询今日邮件
            todayMails: [
                /今天.*邮件/,
                /今日.*邮件/,
                /今天.*收到/,
                /今日.*收到/
            ],
            // 查询重要邮件
            importantMails: [
                /重要.*邮件/,
                /紧急.*邮件/,
                /优先级.*高/,
                /重要.*消息/
            ],
            // 查询待办事项
            todos: [
                /待办.*事项/,
                /任务.*列表/,
                /需要.*处理/,
                /要.*完成/
            ],
            // 邮件摘要
            summary: [
                /摘要/,
                /总结/,
                /概括/,
                /汇总/
            ],
            // 邮件搜索
            search: [
                /搜索/,
                /查找/,
                /找.*邮件/,
                /关于.*的邮件/
            ]
        };
        
        this.initialize();
    }
    
    /**
     * 初始化对话管理器
     */
    async initialize() {
        try {
            this.logger.info('初始化对话管理器');
            
            // 加载历史对话
            await this.loadChatHistory();
            
            // 初始化邮件知识库
            await this.initializeMailKnowledgeBase();
            
            this.logger.info('对话管理器初始化完成');
            
        } catch (error) {
            this.logger.error('对话管理器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 处理用户消息
     */
    async handleUserMessage(message) {
        try {
            this.logger.info('处理用户消息:', message);
            
            // 添加用户消息到历史
            this.addMessage('user', message);
            
            // 分析用户意图
            const intent = this.analyzeIntent(message);
            this.logger.info('识别到用户意图:', intent);
            
            // 根据意图处理请求
            const response = await this.processIntent(intent, message);
            
            // 添加助手回复到历史
            this.addMessage('assistant', response);
            
            // 保存对话历史
            await this.saveChatHistory();
            
            return response;
            
        } catch (error) {
            this.logger.error('处理用户消息失败:', error);
            const errorResponse = {
                type: 'error',
                content: '抱歉，处理您的请求时出现了问题。请稍后重试。',
                error: error.message
            };
            
            this.addMessage('assistant', errorResponse);
            return errorResponse;
        }
    }
    
    /**
     * 分析用户意图
     */
    analyzeIntent(message) {
        const lowerMessage = message.toLowerCase();
        
        for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(lowerMessage)) {
                    return {
                        type: intent,
                        confidence: 0.8,
                        originalMessage: message
                    };
                }
            }
        }
        
        // 如果没有匹配到特定意图，使用通用对话
        return {
            type: 'general',
            confidence: 0.5,
            originalMessage: message
        };
    }
    
    /**
     * 根据意图处理请求
     */
    async processIntent(intent, originalMessage) {
        switch (intent.type) {
            case 'todayMails':
                return await this.handleTodayMailsQuery();
                
            case 'importantMails':
                return await this.handleImportantMailsQuery();
                
            case 'todos':
                return await this.handleTodosQuery();
                
            case 'summary':
                return await this.handleSummaryQuery(originalMessage);
                
            case 'search':
                return await this.handleSearchQuery(originalMessage);
                
            case 'general':
            default:
                return await this.handleGeneralQuery(originalMessage);
        }
    }
    
    /**
     * 处理今日邮件查询
     */
    async handleTodayMailsQuery() {
        try {
            const today = new Date().toDateString();
            const todayMails = this.getTodayMails();
            
            if (todayMails.length === 0) {
                return {
                    type: 'info',
                    content: '今天还没有收到新邮件。',
                    data: { count: 0 }
                };
            }
            
            const importantMails = todayMails.filter(mail => 
                mail.analysis && mail.analysis.importance && mail.analysis.importance.score >= 7
            );
            
            let content = `今天您收到了 ${todayMails.length} 封邮件`;
            if (importantMails.length > 0) {
                content += `，其中 ${importantMails.length} 封比较重要`;
            }
            content += '：\n\n';
            
            // 显示前5封邮件的摘要
            const displayMails = todayMails.slice(0, 5);
            for (const mail of displayMails) {
                const importance = mail.analysis?.importance?.score || 5;
                const importanceIcon = importance >= 8 ? '🔴' : importance >= 6 ? '🟡' : '🟢';
                
                content += `${importanceIcon} **${mail.subject}**\n`;
                content += `发送者: ${mail.sender.display}\n`;
                
                if (mail.analysis?.summary) {
                    content += `摘要: ${mail.analysis.summary.content}\n`;
                }
                content += '\n';
            }
            
            if (todayMails.length > 5) {
                content += `... 还有 ${todayMails.length - 5} 封邮件`;
            }
            
            return {
                type: 'structured',
                content: content,
                data: {
                    totalCount: todayMails.length,
                    importantCount: importantMails.length,
                    mails: displayMails
                }
            };
            
        } catch (error) {
            this.logger.error('处理今日邮件查询失败:', error);
            return {
                type: 'error',
                content: '获取今日邮件信息时出现问题，请稍后重试。'
            };
        }
    }
    
    /**
     * 处理重要邮件查询
     */
    async handleImportantMailsQuery() {
        try {
            const importantMails = this.getImportantMails();
            
            if (importantMails.length === 0) {
                return {
                    type: 'info',
                    content: '目前没有标记为重要的邮件。'
                };
            }
            
            let content = `找到 ${importantMails.length} 封重要邮件：\n\n`;
            
            for (const mail of importantMails.slice(0, 5)) {
                const score = mail.analysis?.importance?.score || 5;
                const level = score >= 8 ? '高' : score >= 6 ? '中' : '低';
                
                content += `🔴 **${mail.subject}**\n`;
                content += `重要性: ${score}/10 (${level})\n`;
                content += `发送者: ${mail.sender.display}\n`;
                
                if (mail.analysis?.importance?.reason) {
                    content += `原因: ${mail.analysis.importance.reason}\n`;
                }
                content += '\n';
            }
            
            return {
                type: 'structured',
                content: content,
                data: {
                    count: importantMails.length,
                    mails: importantMails.slice(0, 5)
                }
            };
            
        } catch (error) {
            this.logger.error('处理重要邮件查询失败:', error);
            return {
                type: 'error',
                content: '获取重要邮件信息时出现问题，请稍后重试。'
            };
        }
    }
    
    /**
     * 处理待办事项查询
     */
    async handleTodosQuery() {
        try {
            const allTodos = this.getAllTodos();
            
            if (allTodos.length === 0) {
                return {
                    type: 'info',
                    content: '目前没有待办事项。'
                };
            }
            
            // 按优先级分组
            const highPriority = allTodos.filter(todo => todo.priority === 'high');
            const mediumPriority = allTodos.filter(todo => todo.priority === 'medium');
            const lowPriority = allTodos.filter(todo => todo.priority === 'low');
            
            let content = `您有 ${allTodos.length} 个待办事项：\n\n`;
            
            if (highPriority.length > 0) {
                content += '🔴 **高优先级:**\n';
                highPriority.forEach(todo => {
                    content += `• ${todo.task}\n`;
                });
                content += '\n';
            }
            
            if (mediumPriority.length > 0) {
                content += '🟡 **中优先级:**\n';
                mediumPriority.forEach(todo => {
                    content += `• ${todo.task}\n`;
                });
                content += '\n';
            }
            
            if (lowPriority.length > 0) {
                content += '🟢 **低优先级:**\n';
                lowPriority.forEach(todo => {
                    content += `• ${todo.task}\n`;
                });
            }
            
            return {
                type: 'structured',
                content: content,
                data: {
                    totalCount: allTodos.length,
                    highPriority: highPriority.length,
                    mediumPriority: mediumPriority.length,
                    lowPriority: lowPriority.length,
                    todos: allTodos
                }
            };
            
        } catch (error) {
            this.logger.error('处理待办事项查询失败:', error);
            return {
                type: 'error',
                content: '获取待办事项时出现问题，请稍后重试。'
            };
        }
    }
    
    /**
     * 处理通用查询
     */
    async handleGeneralQuery(message) {
        try {
            // 使用AI模型进行通用对话
            const prompt = this.buildGeneralChatPrompt(message);
            
            const response = await this.apiClient.sendChatRequest([
                { role: 'user', content: prompt }
            ], {
                maxTokens: 500,
                temperature: 0.7
            });
            
            return {
                type: 'chat',
                content: response.content,
                model: response.model
            };
            
        } catch (error) {
            this.logger.error('处理通用查询失败:', error);
            return {
                type: 'fallback',
                content: '我理解您的问题，但目前无法提供准确的回答。您可以尝试询问关于邮件、待办事项或重要性评估的问题。'
            };
        }
    }
    
    /**
     * 构建通用对话提示词
     */
    buildGeneralChatPrompt(userMessage) {
        const context = this.getMailContext();
        
        return `你是一个智能邮件助手，专门帮助用户管理和分析邮件。

当前邮件上下文：
- 今日邮件数量: ${context.todayCount}
- 重要邮件数量: ${context.importantCount}  
- 待办事项数量: ${context.todoCount}

用户问题: ${userMessage}

请以友好、专业的语气回答用户的问题。如果问题与邮件管理相关，请提供具体的帮助。如果问题超出邮件助手的范围，请礼貌地说明并引导用户询问邮件相关的问题。

回答要求：
1. 简洁明了，不超过200字
2. 语气友好专业
3. 如果可能，提供具体的建议或操作指导`;
    }
    
    /**
     * 获取邮件上下文信息
     */
    getMailContext() {
        const todayMails = this.getTodayMails();
        const importantMails = this.getImportantMails();
        const allTodos = this.getAllTodos();
        
        return {
            todayCount: todayMails.length,
            importantCount: importantMails.length,
            todoCount: allTodos.length
        };
    }
    
    /**
     * 获取今日邮件
     */
    getTodayMails() {
        const today = new Date().toDateString();
        const mails = [];
        
        for (const [mailId, mailData] of this.mailKnowledgeBase) {
            if (mailData.receivedDate && 
                new Date(mailData.receivedDate).toDateString() === today) {
                mails.push(mailData);
            }
        }
        
        return mails.sort((a, b) => 
            new Date(b.receivedDate) - new Date(a.receivedDate)
        );
    }
    
    /**
     * 获取重要邮件
     */
    getImportantMails() {
        const mails = [];
        
        for (const [mailId, mailData] of this.mailKnowledgeBase) {
            if (mailData.analysis?.importance?.score >= 7) {
                mails.push(mailData);
            }
        }
        
        return mails.sort((a, b) => 
            (b.analysis?.importance?.score || 0) - (a.analysis?.importance?.score || 0)
        );
    }
    
    /**
     * 获取所有待办事项
     */
    getAllTodos() {
        const todos = [];
        
        for (const [mailId, mailData] of this.mailKnowledgeBase) {
            if (mailData.analysis?.todos?.todos) {
                mailData.analysis.todos.todos.forEach(todo => {
                    todos.push({
                        ...todo,
                        mailId: mailId,
                        mailSubject: mailData.subject
                    });
                });
            }
        }
        
        return todos;
    }
    
    /**
     * 添加消息到对话历史
     */
    addMessage(role, content) {
        const message = {
            id: Date.now() + Math.random(),
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        };
        
        this.chatHistory.push(message);
        
        // 限制历史记录长度
        if (this.chatHistory.length > 100) {
            this.chatHistory = this.chatHistory.slice(-50);
        }
    }
    
    /**
     * 获取对话历史
     */
    getChatHistory() {
        return this.chatHistory;
    }
    
    /**
     * 清空对话历史
     */
    clearChatHistory() {
        this.chatHistory = [];
        this.saveChatHistory();
    }
    
    /**
     * 初始化邮件知识库
     */
    async initializeMailKnowledgeBase() {
        try {
            // 从存储中加载邮件数据
            const storedMails = localStorage.getItem('ai-assistant:mail-knowledge-base');
            if (storedMails) {
                const mailsData = JSON.parse(storedMails);
                for (const [mailId, mailData] of Object.entries(mailsData)) {
                    this.mailKnowledgeBase.set(mailId, mailData);
                }
            }
            
            this.logger.info(`邮件知识库初始化完成，包含 ${this.mailKnowledgeBase.size} 封邮件`);
            
        } catch (error) {
            this.logger.error('初始化邮件知识库失败:', error);
        }
    }
    
    /**
     * 添加邮件到知识库
     */
    addMailToKnowledgeBase(mailInfo, analysisResults) {
        const mailData = {
            ...mailInfo,
            analysis: analysisResults,
            addedAt: new Date().toISOString(),
            receivedDate: mailInfo.receivedDate || new Date().toISOString()
        };
        
        this.mailKnowledgeBase.set(mailInfo.id, mailData);
        this.saveMailKnowledgeBase();
        
        this.logger.info(`邮件已添加到知识库: ${mailInfo.subject}`);
    }
    
    /**
     * 保存邮件知识库
     */
    saveMailKnowledgeBase() {
        try {
            const mailsData = {};
            for (const [mailId, mailData] of this.mailKnowledgeBase) {
                mailsData[mailId] = mailData;
            }
            
            localStorage.setItem('ai-assistant:mail-knowledge-base', 
                JSON.stringify(mailsData));
                
        } catch (error) {
            this.logger.error('保存邮件知识库失败:', error);
        }
    }
    
    /**
     * 加载对话历史
     */
    async loadChatHistory() {
        try {
            const storedHistory = localStorage.getItem('ai-assistant:chat-history');
            if (storedHistory) {
                this.chatHistory = JSON.parse(storedHistory);
            }
        } catch (error) {
            this.logger.error('加载对话历史失败:', error);
            this.chatHistory = [];
        }
    }
    
    /**
     * 保存对话历史
     */
    async saveChatHistory() {
        try {
            localStorage.setItem('ai-assistant:chat-history', 
                JSON.stringify(this.chatHistory));
        } catch (error) {
            this.logger.error('保存对话历史失败:', error);
        }
    }
}
