# AI邮件助手 - 开发者文档

## 项目概述

AI邮件助手是一个基于Coremail插件架构的智能邮件分析工具，采用模块化设计，集成大模型API实现邮件内容的智能分析。

## 技术架构

### 技术栈
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **模块化**: ES6 Modules
- **API集成**: Fetch API
- **数据存储**: LocalStorage + SessionStorage
- **插件框架**: Coremail Plugin API

### 架构设计
```
┌─────────────────────────────────────────┐
│                UI Layer                 │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Main UI     │  │ Config UI       │   │
│  │ (sidebar.js)│  │ (config.js)     │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│              Core Layer                 │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ AI Analyzer │  │ Mail Monitor    │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ API Client  │  │ Config Manager  │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│              Utils Layer                │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Logger      │  │ Storage Manager │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Error       │  │ Performance     │   │
│  │ Handler     │  │ Monitor         │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 核心模块

### 1. API客户端 (api-client.js)

负责与AI模型API的通信，支持多种API提供商。

```javascript
// 基本用法
const apiClient = new APIClient();
await apiClient.initialize();

// 发送请求
const response = await apiClient.sendChatRequest([
    { role: 'user', content: '分析这封邮件...' }
]);
```

**主要方法**:
- `initialize()`: 初始化客户端
- `sendChatRequest(messages, options)`: 发送聊天请求
- `testConnection()`: 测试API连接
- `updateConfig(config)`: 更新配置

### 2. AI分析器 (ai-analyzer.js)

核心分析引擎，负责邮件内容的智能分析。

```javascript
// 基本用法
const analyzer = new AIAnalyzer();
await analyzer.initialize();

// 分析邮件
const results = await analyzer.analyzeEmail(mailInfo);
```

**分析功能**:
- `generateSummary()`: 生成摘要
- `evaluateImportance()`: 评估重要性
- `extractTodos()`: 提取待办事项
- `extractKeyInfo()`: 提取关键信息

### 3. 邮件监控器 (mail-monitor.js)

监控邮件变化，获取邮件数据。

```javascript
// 基本用法
const monitor = new MailMonitor();
await monitor.initialize();

// 监听邮件变化
monitor.on('mailChanged', (mailInfo) => {
    console.log('邮件已变化:', mailInfo);
});
```

### 4. 配置管理器 (config-manager.js)

管理用户配置和系统设置。

```javascript
// 基本用法
const config = new ConfigManager();
await config.initialize();

// 获取配置
const apiConfig = config.getAPIConfig();

// 设置配置
await config.setConfig('apiKey', 'new_key');
```

## 开发环境搭建

### 环境要求
- Node.js 16+ (用于开发工具)
- 现代浏览器 (Chrome 80+, Firefox 75+)
- Coremail邮件客户端 (支持插件)

### 开发步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd coremail-ai-assistant
```

2. **安装依赖** (如果有)
```bash
npm install  # 如果使用构建工具
```

3. **开发调试**
```bash
# 启动本地服务器 (可选)
python -m http.server 8000
# 或使用其他静态服务器
```

4. **插件安装**
- 将 `app/plugin` 目录安装到Coremail
- 在浏览器中打开开发者工具进行调试

## 插件开发规范

### Coremail插件配置

插件配置文件 `setting.js` 定义了插件的基本信息和入口点：

```javascript
{
  "toolbar": [
    {
      "tab": "mail",
      "type": "button",
      "name": "ai_assistant",
      "label": { "zh_CN": "AI助手" },
      "url": "plugin:index.html",
      "target": "beside"
    }
  ]
}
```

### 插件API使用

```javascript
// 获取邮件信息
CMPlugin.getAttributes({
    "mailinfo": "",
    "sid": "",
    "mid": ""
}, (result) => {
    const mailInfo = result.mailinfo;
    // 处理邮件信息
});
```

## 代码规范

### JavaScript规范

1. **ES6+ 语法**
```javascript
// 使用 const/let 而不是 var
const apiClient = new APIClient();
let currentMail = null;

// 使用箭头函数
const processEmail = (email) => {
    return email.content.trim();
};

// 使用模板字符串
const message = `分析邮件: ${email.subject}`;
```

2. **模块化**
```javascript
// 导出
export class APIClient {
    // ...
}

// 导入
import { APIClient } from './core/api-client.js';
```

3. **异步处理**
```javascript
// 使用 async/await
async function analyzeEmail(email) {
    try {
        const result = await apiClient.sendRequest(email);
        return result;
    } catch (error) {
        logger.error('分析失败:', error);
        throw error;
    }
}
```

### CSS规范

1. **CSS变量**
```css
:root {
    --primary-color: #2563eb;
    --text-primary: #1e293b;
    --spacing-md: 16px;
}
```

2. **BEM命名**
```css
.analysis-card {}
.analysis-card__title {}
.analysis-card__content {}
.analysis-card--loading {}
```

### 错误处理

1. **统一错误处理**
```javascript
import { globalErrorHandler, AIAssistantError } from './utils/error-handler.js';

// 抛出自定义错误
throw new AIAssistantError(
    'API请求失败',
    ERROR_TYPES.API,
    ERROR_SEVERITY.HIGH
);

// 处理错误
globalErrorHandler.handleError(error, { context: 'email-analysis' });
```

2. **日志记录**
```javascript
import { Logger } from './utils/logger.js';

const logger = new Logger('ModuleName');
logger.info('操作成功');
logger.error('操作失败:', error);
```

## 测试

### 单元测试

使用内置的测试框架进行单元测试：

```javascript
// 测试示例
runner.test('API客户端应该能够正确初始化', async () => {
    const apiClient = new APIClient();
    await apiClient.initialize();
    
    assert.isNotNull(apiClient.config, '配置应该被加载');
    assert.equals(apiClient.config.provider, 'siliconflow');
});
```

### 集成测试

```javascript
// 集成测试示例
runner.test('完整的邮件分析流程', async () => {
    const testMail = testUtils.createTestMail();
    const analyzer = new AIAnalyzer();
    
    await analyzer.initialize();
    const results = await analyzer.analyzeEmail(testMail);
    
    assert.hasProperty(results, 'summary');
    assert.hasProperty(results, 'importance');
});
```

### 运行测试

打开 `tests/test-runner.html` 在浏览器中运行测试套件。

## 性能优化

### 1. 代码优化
- 使用防抖和节流优化频繁操作
- 实现结果缓存减少重复请求
- 延迟加载非关键模块

### 2. 网络优化
- 实现请求重试机制
- 使用适当的超时设置
- 压缩请求数据

### 3. 内存管理
- 及时清理事件监听器
- 限制缓存大小
- 避免内存泄漏

## 扩展开发

### 添加新的AI提供商

1. **更新API_PROVIDERS配置**
```javascript
const API_PROVIDERS = {
    // 现有提供商...
    newProvider: {
        name: '新提供商',
        baseUrl: 'https://api.newprovider.com',
        models: {
            'model-1': 'Model 1',
            'model-2': 'Model 2'
        },
        headers: (apiKey) => ({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        })
    }
};
```

2. **实现特定的请求格式**
```javascript
buildRequestBody(provider, messages, options) {
    switch (this.config.provider) {
        case 'newProvider':
            return {
                // 新提供商的请求格式
                model: options.model,
                messages: messages,
                // 其他特定参数
            };
        // 其他情况...
    }
}
```

### 添加新的分析功能

1. **定义提示词模板**
```javascript
const ANALYSIS_PROMPTS = {
    newFeature: {
        'zh-CN': '新功能的中文提示词模板...',
        'en': 'New feature English prompt template...'
    }
};
```

2. **实现分析方法**
```javascript
async extractNewFeature(mailInfo, language = 'zh-CN') {
    const prompt = this.buildPrompt('newFeature', mailInfo, language);
    
    const response = await this.apiClient.sendChatRequest([
        { role: 'user', content: prompt }
    ]);
    
    return this.parseNewFeatureResponse(response);
}
```

## 部署指南

### 开发环境部署
1. 直接将 `app/plugin` 目录安装到Coremail
2. 在浏览器开发者工具中调试

### 生产环境部署
1. 代码压缩和优化
2. 资源文件打包
3. 版本控制和更新机制

### 分发打包
```bash
# 创建分发包
zip -r ai-assistant-v1.0.zip app/plugin/
```

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查Coremail版本兼容性
   - 验证插件目录结构
   - 查看浏览器控制台错误

2. **API调用失败**
   - 验证API密钥正确性
   - 检查网络连接
   - 查看API服务状态

3. **性能问题**
   - 检查内存使用情况
   - 优化缓存策略
   - 调整请求参数

### 调试技巧

1. **启用调试日志**
```javascript
// 在配置中启用调试模式
config.setConfig('enableDebugLog', true);
```

2. **使用性能监控**
```javascript
import { globalPerformanceMonitor } from './utils/performance-monitor.js';

// 监控API调用性能
globalPerformanceMonitor.startTimer('api_call');
// ... API调用
globalPerformanceMonitor.endTimer('api_call');
```

## 贡献指南

### 代码贡献
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 文档贡献
1. 改进现有文档
2. 添加使用示例
3. 翻译多语言版本

---

*本文档持续更新中，欢迎贡献和反馈。*
