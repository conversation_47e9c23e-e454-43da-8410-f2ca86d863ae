<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-icon {
            background: #007bff;
            color: white;
        }
        .btn-icon:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AI邮件助手 - 按钮功能测试</h1>
        
        <div class="test-section">
            <h3>1. 原始按钮测试</h3>
            <p>这些是从主页面复制的原始按钮：</p>
            <button id="refreshBtn" class="btn btn-icon" title="刷新分析">
                <span class="icon">🔄</span>
            </button>
            <button id="configBtn" class="btn btn-icon" title="设置">
                <span class="icon">⚙️</span>
            </button>
            <div id="originalResult" class="test-result info">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>2. 简单测试按钮</h3>
            <p>这些是简单的测试按钮：</p>
            <button id="testRefresh" class="btn btn-icon">🔄 测试刷新</button>
            <button id="testConfig" class="btn btn-icon">⚙️ 测试设置</button>
            <div id="simpleResult" class="test-result info">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>3. 诊断信息</h3>
            <div id="diagnostics" class="test-result info">正在检查...</div>
        </div>
        
        <div class="test-section">
            <h3>4. 控制台日志</h3>
            <p>请打开浏览器开发者工具查看控制台输出</p>
            <button id="logTest" class="btn btn-icon">📝 输出测试日志</button>
        </div>
    </div>

    <script>
        // 诊断函数
        function runDiagnostics() {
            const diagnostics = document.getElementById('diagnostics');
            let info = [];
            
            // 检查DOM元素
            const refreshBtn = document.getElementById('refreshBtn');
            const configBtn = document.getElementById('configBtn');
            
            info.push(`刷新按钮元素: ${refreshBtn ? '✅ 存在' : '❌ 不存在'}`);
            info.push(`设置按钮元素: ${configBtn ? '✅ 存在' : '❌ 不存在'}`);
            
            // 检查事件监听器
            info.push(`页面加载状态: ${document.readyState}`);
            info.push(`当前时间: ${new Date().toLocaleString()}`);
            
            // 检查全局对象
            info.push(`window.aiAssistant: ${window.aiAssistant ? '✅ 存在' : '❌ 不存在'}`);
            info.push(`CMPlugin: ${typeof CMPlugin !== 'undefined' ? '✅ 存在' : '❌ 不存在'}`);
            
            diagnostics.innerHTML = info.join('<br>');
        }
        
        // 等待DOM加载
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面DOM加载完成');
            
            // 运行诊断
            runDiagnostics();
            
            // 测试原始按钮
            const refreshBtn = document.getElementById('refreshBtn');
            const configBtn = document.getElementById('configBtn');
            const originalResult = document.getElementById('originalResult');
            
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    console.log('原始刷新按钮被点击');
                    originalResult.className = 'test-result success';
                    originalResult.textContent = '✅ 刷新按钮点击成功！时间: ' + new Date().toLocaleTimeString();
                });
            }
            
            if (configBtn) {
                configBtn.addEventListener('click', function() {
                    console.log('原始设置按钮被点击');
                    originalResult.className = 'test-result success';
                    originalResult.textContent = '✅ 设置按钮点击成功！时间: ' + new Date().toLocaleTimeString();
                    
                    // 尝试打开配置页面
                    try {
                        const configWindow = window.open('config.html', 'test_config', 'width=800,height=600');
                        if (!configWindow) {
                            originalResult.textContent += ' (弹窗被阻止)';
                        }
                    } catch (error) {
                        console.error('打开配置页面失败:', error);
                    }
                });
            }
            
            // 测试简单按钮
            const testRefresh = document.getElementById('testRefresh');
            const testConfig = document.getElementById('testConfig');
            const simpleResult = document.getElementById('simpleResult');
            
            testRefresh.addEventListener('click', function() {
                console.log('测试刷新按钮被点击');
                simpleResult.className = 'test-result success';
                simpleResult.textContent = '✅ 测试刷新按钮工作正常！';
            });
            
            testConfig.addEventListener('click', function() {
                console.log('测试设置按钮被点击');
                simpleResult.className = 'test-result success';
                simpleResult.textContent = '✅ 测试设置按钮工作正常！';
            });
            
            // 日志测试按钮
            const logTest = document.getElementById('logTest');
            logTest.addEventListener('click', function() {
                console.log('=== 按钮测试日志 ===');
                console.log('页面URL:', window.location.href);
                console.log('用户代理:', navigator.userAgent);
                console.log('DOM元素检查:');
                console.log('- refreshBtn:', document.getElementById('refreshBtn'));
                console.log('- configBtn:', document.getElementById('configBtn'));
                console.log('全局对象检查:');
                console.log('- window.aiAssistant:', window.aiAssistant);
                console.log('- CMPlugin:', typeof CMPlugin !== 'undefined' ? CMPlugin : 'undefined');
                console.log('=== 日志结束 ===');
                
                alert('测试日志已输出到控制台，请查看开发者工具');
            });
            
            // 每5秒更新一次诊断信息
            setInterval(runDiagnostics, 5000);
        });
    </script>
</body>
</html>
