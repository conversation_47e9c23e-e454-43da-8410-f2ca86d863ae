<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI邮件助手</title>
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <!-- 加载Coremail插件API -->
    <script src="plugin:plugin.js"></script>
    
    <div id="app" class="ai-assistant-container">
        <!-- 头部工具栏 -->
        <header class="header">
            <div class="header-title">
                <h2>AI邮件助手</h2>
                <span class="version">v1.0</span>
            </div>
            <div class="header-actions">
                <button id="refreshBtn" class="btn btn-icon" title="刷新分析">
                    <span class="icon">🔄</span>
                </button>
                <button id="configBtn" class="btn btn-icon" title="设置">
                    <span class="icon">⚙️</span>
                </button>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 状态指示器 -->
            <div id="statusIndicator" class="status-indicator">
                <div class="status-item">
                    <span class="status-label">连接状态:</span>
                    <span id="connectionStatus" class="status-value">未连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">分析状态:</span>
                    <span id="analysisStatus" class="status-value">待机</span>
                </div>
            </div>

            <!-- 对话式AI助手 -->
            <section id="chatSection" class="section">
                <h3 class="section-title">💬 AI助手对话</h3>

                <!-- 对话窗口 -->
                <div id="chatWindow" class="chat-window">
                    <div id="chatMessages" class="chat-messages">
                        <div class="message assistant-message">
                            <div class="message-avatar">🤖</div>
                            <div class="message-content">
                                <p>您好！我是您的AI邮件助手。我正在监控您的邮件，可以帮您：</p>
                                <ul>
                                    <li>📧 自动分析新邮件</li>
                                    <li>📋 提取待办事项</li>
                                    <li>⭐ 评估邮件重要性</li>
                                    <li>🔍 回答邮件相关问题</li>
                                </ul>
                                <p>请问有什么可以帮助您的吗？</p>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作按钮 -->
                    <div class="quick-actions">
                        <button class="quick-btn" data-question="今天我有什么重要邮件？">📧 今日重要邮件</button>
                        <button class="quick-btn" data-question="帮我总结一下最近的邮件">📄 邮件摘要</button>
                        <button class="quick-btn" data-question="我有哪些待办事项？">📋 待办事项</button>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="chat-input-area">
                    <div class="input-container">
                        <input type="text" id="chatInput" placeholder="请输入您的问题..." maxlength="500">
                        <button id="sendBtn" class="send-btn" title="发送">
                            <span class="send-icon">📤</span>
                        </button>
                    </div>
                    <div class="input-hint">
                        <span>💡 提示：您可以问我关于邮件的任何问题</span>
                    </div>
                </div>
            </section>

            <!-- 邮件监控状态 -->
            <section id="monitorSection" class="section">
                <h3 class="section-title">📊 邮件监控</h3>
                <div class="monitor-info">
                    <div class="monitor-item">
                        <span class="monitor-label">监控状态:</span>
                        <span id="monitorStatus" class="status-value">启动中</span>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">新邮件:</span>
                        <span id="newMailCount" class="status-value">0</span>
                    </div>
                    <div class="monitor-item">
                        <span class="monitor-label">今日分析:</span>
                        <span id="todayAnalysisCount" class="status-value">0</span>
                    </div>
                </div>
            </section>

            <!-- AI分析结果 -->
            <section id="analysisSection" class="section">
                <h3 class="section-title">AI分析结果</h3>
                
                <!-- 邮件摘要 -->
                <div id="summaryCard" class="analysis-card">
                    <h4 class="card-title">📄 邮件摘要</h4>
                    <div id="summaryContent" class="card-content">
                        <p class="placeholder">暂无分析结果</p>
                    </div>
                </div>

                <!-- 重要性评估 -->
                <div id="importanceCard" class="analysis-card">
                    <h4 class="card-title">⭐ 重要性评估</h4>
                    <div id="importanceContent" class="card-content">
                        <div class="importance-level">
                            <span class="level-label">重要程度:</span>
                            <div id="importanceBar" class="importance-bar">
                                <div class="bar-fill" style="width: 0%"></div>
                            </div>
                            <span id="importanceText" class="level-text">未评估</span>
                        </div>
                        <div id="importanceReason" class="importance-reason">
                            <p class="placeholder">暂无评估结果</p>
                        </div>
                    </div>
                </div>

                <!-- 待办事项 -->
                <div id="todoCard" class="analysis-card">
                    <h4 class="card-title">📋 待办事项</h4>
                    <div id="todoContent" class="card-content">
                        <ul id="todoList" class="todo-list">
                            <li class="placeholder">暂无待办事项</li>
                        </ul>
                    </div>
                </div>

                <!-- 关键信息提取 -->
                <div id="keyInfoCard" class="analysis-card">
                    <h4 class="card-title">🔍 关键信息</h4>
                    <div id="keyInfoContent" class="card-content">
                        <div class="key-info-grid">
                            <div class="info-item">
                                <span class="info-label">时间信息:</span>
                                <span id="timeInfo" class="info-value">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">联系人:</span>
                                <span id="contactInfo" class="info-value">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">主题分类:</span>
                                <span id="categoryInfo" class="info-value">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 操作按钮区域 -->
            <section class="actions-section">
                <button id="exportBtn" class="btn btn-secondary" disabled>
                    📤 导出对话记录
                </button>
                <button id="clearChatBtn" class="btn btn-outline">
                    🗑️ 清空对话
                </button>
            </section>
        </main>

        <!-- 加载指示器 -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>AI正在分析邮件内容...</p>
            </div>
        </div>

        <!-- 错误提示 -->
        <div id="errorToast" class="error-toast" style="display: none;">
            <span class="error-icon">❌</span>
            <span id="errorMessage" class="error-message"></span>
            <button id="closeErrorBtn" class="close-btn">×</button>
        </div>

        <!-- 成功提示 -->
        <div id="successToast" class="success-toast" style="display: none;">
            <span class="success-icon">✅</span>
            <span id="successMessage" class="success-message"></span>
            <button id="closeSuccessBtn" class="close-btn">×</button>
        </div>
    </div>

    <!-- JavaScript模块 -->
    <script type="module" src="js/main.js"></script>
</body>
</html>
